"use client";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON>, Card, Callout } from "@radix-ui/themes";
import { signIn } from "@/lib/auth-client";
import { Code, Github } from "lucide-react";

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleGitHubSignIn = async () => {
    setIsLoading(true);
    setError("");

    try {
      const result = await signIn.social({
        provider: "github",
        callbackURL: "/dashboard",
      });

      if (result.error) {
        setError(result.error.message || "Failed to sign in with GitHub");
      }
    } catch (err) {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-muted/50 py-8 xs:py-12 mobile-padding">
      <div className="w-full max-w-md space-y-6 xs:space-y-8">
        <div className="text-center">
          <Link href="/" className="inline-flex items-center gap-2 mb-6 xs:mb-8 touch-target">
            <Code className="h-6 w-6 xs:h-8 xs:w-8 text-primary" />
            <span className="font-bold text-xl xs:text-2xl">OnlyRules</span>
          </Link>
        </div>

        <Card className="space-y-4 xs:space-y-6 mobile-card">
          <div className="space-y-2">
            <div className="text-xl xs:text-2xl text-center font-semibold">Welcome back</div>
            <div className="text-center text-muted-foreground text-sm xs:text-base">
              Sign in with your GitHub account to continue
            </div>
          </div>
          <div className="space-y-4 xs:space-y-6">
            {error && (
              <Callout.Root color="red">
                <Callout.Text className="text-sm">{error}</Callout.Text>
              </Callout.Root>
            )}

            <Button
              onClick={handleGitHubSignIn}
              className="w-full touch-target"
              disabled={isLoading}
              size="3"
            >
              <Github className="mr-2 h-4 w-4 xs:h-5 xs:w-5" />
              <span className="text-sm xs:text-base">
                {isLoading ? "Signing in..." : "Continue with GitHub"}
              </span>
            </Button>

            <div className="text-center text-xs xs:text-sm text-muted-foreground leading-relaxed px-2">
              By signing in, you agree to our terms of service and privacy policy.
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}