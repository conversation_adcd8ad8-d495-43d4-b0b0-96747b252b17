/* Docs-specific styles optimized for Tailwind CSS integration */

/* Ensure fumadocs-ui styles work well with Tailwind */
.fumadocs-ui {
  @apply font-sans;
}

/* Layout components using Tailwind classes */
.docs-layout {
  @apply min-h-screen flex flex-col;
}

.docs-sidebar {
  @apply bg-card border-r border-border;
}

.docs-content {
  @apply flex-1 p-6;
}

.docs-nav {
  @apply bg-card border-b border-border;
}

/* Typography using Tailwind classes */
.docs-content h1,
.docs-content h2,
.docs-content h3,
.docs-content h4,
.docs-content h5,
.docs-content h6 {
  @apply text-foreground mt-8 mb-4 font-semibold;
}

.docs-content h1 {
  @apply text-4xl;
}

.docs-content h2 {
  @apply text-3xl;
}

.docs-content h3 {
  @apply text-2xl;
}

.docs-content h4 {
  @apply text-xl;
}

.docs-content h5 {
  @apply text-lg;
}

.docs-content h6 {
  @apply text-base;
}

.docs-content p {
  @apply text-muted-foreground leading-relaxed mb-4;
}

.docs-content a {
  @apply text-primary hover:text-primary/80 transition-colors no-underline hover:underline;
}

/* Code styling using Tailwind classes */
.docs-content code {
  @apply bg-muted text-foreground px-1.5 py-0.5 rounded text-sm font-mono;
}

.docs-content pre {
  @apply bg-muted/50 border border-border rounded-lg p-4 overflow-x-auto;
}

.docs-content pre code {
  @apply bg-transparent p-0 rounded-none;
}

/* List styling using Tailwind classes */
.docs-content ul,
.docs-content ol {
  @apply text-muted-foreground mb-4 pl-6;
}

.docs-content li {
  @apply mb-1;
}

/* Blockquote styling using Tailwind classes */
.docs-content blockquote {
  @apply border-l-4 border-border pl-4 my-6 text-muted-foreground italic;
}

/* Table styling using Tailwind classes */
.docs-content table {
  @apply w-full border-collapse border border-border my-6;
}

.docs-content th,
.docs-content td {
  @apply border border-border px-4 py-2 text-left;
}

.docs-content th {
  @apply bg-muted font-semibold text-foreground;
}

.docs-content td {
  @apply text-muted-foreground;
}

/* Image styling using Tailwind classes */
.docs-content img {
  @apply max-w-full h-auto rounded-lg border border-border my-4;
}

/* Horizontal rule styling */
.docs-content hr {
  @apply border-t border-border my-8;
}

/* Ensure proper spacing for content sections */
.docs-content > *:first-child {
  @apply mt-0;
}

.docs-content > *:last-child {
  @apply mb-0;
} 