{"/api/search/route": "/api/search", "/api/auth/[...all]/route": "/api/auth/[...all]", "/api/robots.txt/route": "/api/robots.txt", "/api/rules/[id]/route": "/api/rules/[id]", "/api/rules/raw/route": "/api/rules/raw", "/api/rules/export/route": "/api/rules/export", "/api/rules/download/route": "/api/rules/download", "/api/sitemap.xml/route": "/api/sitemap.xml", "/api/rules/route": "/api/rules", "/api/tags/route": "/api/tags", "/api/rulesets/route": "/api/rulesets", "/sitemap.xml/route": "/sitemap.xml", "/api/rulesets/[id]/route": "/api/rulesets/[id]", "/_not-found/page": "/_not-found", "/(main)/test-theme/page": "/test-theme", "/(main)/auth/signup/page": "/auth/signup", "/(main)/page": "/", "/(main)/auth/signin/page": "/auth/signin", "/(main)/dashboard/page": "/dashboard", "/(main)/rulesets/[id]/page": "/rulesets/[id]", "/(main)/profile/[username]/page": "/profile/[username]", "/(main)/rulesets/page": "/rulesets", "/(main)/rules/[id]/page": "/rules/[id]", "/(main)/r/[id]/page": "/r/[id]", "/(main)/templates/page": "/templates", "/(main)/tutorials/page": "/tutorials", "/(main)/docs/[[...slug]]/page": "/docs/[[...slug]]", "/(main)/ides/claude/page": "/ides/claude", "/(main)/ides/github-copilot/page": "/ides/github-copilot", "/(main)/ides/cursor/page": "/ides/cursor", "/(main)/ides/cline/page": "/ides/cline", "/(main)/ides/windsurf/page": "/ides/windsurf", "/(main)/ides/augment/page": "/ides/augment", "/(main)/ides/page": "/ides", "/(main)/(static)/privacy/page": "/privacy", "/(main)/(static)/terms/page": "/terms", "/(main)/(static)/page": "/"}