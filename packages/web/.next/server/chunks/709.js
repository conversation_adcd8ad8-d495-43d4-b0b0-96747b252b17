exports.id=709,exports.ids=[709],exports.modules={176:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(13486);function e({reset:a}){return(0,d.jsx)("html",{children:(0,d.jsx)("body",{children:(0,d.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",fontFamily:"system-ui, sans-serif",padding:"20px"},children:[(0,d.jsx)("h1",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"Something went wrong!"}),(0,d.jsx)("p",{style:{marginBottom:"2rem",textAlign:"center"},children:"We encountered an unexpected error. Please try again."}),(0,d.jsx)("button",{onClick:a,style:{padding:"12px 24px",backgroundColor:"#0070f3",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"1rem"},children:"Try again"})]})})})}},1934:(a,b,c)=>{"use strict";c.d(b,{QueryProvider:()=>g});var d=c(13486),e=c(84364),f=c(53720);function g({children:a}){return(0,d.jsxs)(e.Ht,{client:f.q,children:[a,!1]})}},10393:(a,b,c)=>{"use strict";c.d(b,{JotaiProvider:()=>f});var d=c(13486),e=c(36114);function f({children:a}){return(0,d.jsx)(e.Kq,{children:a})}},10584:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(38828);function e(){return(0,d.jsx)("html",{children:(0,d.jsx)("body",{children:(0,d.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",fontFamily:"system-ui, sans-serif",padding:"20px"},children:[(0,d.jsx)("h1",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"404 - Not Found"}),(0,d.jsx)("p",{style:{marginBottom:"2rem",textAlign:"center"},children:"The page you're looking for doesn't exist."}),(0,d.jsx)("a",{href:"/",style:{padding:"12px 24px",backgroundColor:"#0070f3",color:"white",textDecoration:"none",borderRadius:"6px",fontSize:"1rem"},children:"Go home"})]})})})}},11363:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,49989,23)),Promise.resolve().then(c.bind(c,21160)),Promise.resolve().then(c.bind(c,10393)),Promise.resolve().then(c.bind(c,1934)),Promise.resolve().then(c.bind(c,84099)),Promise.resolve().then(c.bind(c,92403))},12043:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,42671,23)),Promise.resolve().then(c.bind(c,52548)),Promise.resolve().then(c.bind(c,99023)),Promise.resolve().then(c.bind(c,79104)),Promise.resolve().then(c.bind(c,87649)),Promise.resolve().then(c.bind(c,12781))},12781:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/ui/sonner.tsx","Toaster")},17217:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w,dynamic:()=>t,metadata:()=>u,viewport:()=>v});var d=c(38828);c(21971);var e=c(87649),f=c(99023),g=c(79104),h=c(52548),i=c(65208);let j=["en","zh-CN","zh-HK"];async function k(){let a=(await (0,i.cookies)()).get("locale");if(a&&j.includes(a.value))return a.value;let b=(await (0,i.b3)()).get("accept-language");if(b){let a=b.split(",").map(a=>a.split(";")[0].trim().toLowerCase()).find(a=>{if(j.includes(a))return!0;if(a.startsWith("zh"))return a.includes("hk")||a.includes("tw")?j.includes("zh-HK"):j.includes("zh-CN");let b=a.split("-")[0];return j.includes(b)});if(a){if(a.startsWith("zh"))return a.includes("hk")||a.includes("tw")?"zh-HK":"zh-CN";let b=a.split("-")[0];if(j.includes(b))return b;if(j.includes(a))return a}}return"en"}var l=c(61365),m=c(42671),n=c.n(m);function o(){return(0,d.jsx)("nav",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsxs)("div",{className:"container mx-auto flex h-16 items-center justify-between px-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-8",children:[(0,d.jsx)(n(),{href:"/",className:"flex items-center gap-2",children:(0,d.jsx)("span",{className:"font-bold text-xl text-foreground",children:"OnlyRules"})}),(0,d.jsxs)("div",{className:"hidden md:flex items-center gap-6",children:[(0,d.jsx)(n(),{href:"/templates",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Templates"}),(0,d.jsx)(n(),{href:"/tutorials",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Tutorials"}),(0,d.jsx)(n(),{href:"/dashboard",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Dashboard"})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)(n(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"GitHub"}),(0,d.jsx)(n(),{href:"/auth/signin",className:"bg-primary text-primary-foreground px-4 py-2 rounded text-sm hover:bg-primary/90 transition-colors",children:"Sign In"})]})]})})}function p(){return(0,d.jsx)(o,{})}async function q(){try{let a=await k();return(0,d.jsx)(h.ClientNavbar,{locale:a})}catch(a){return(0,d.jsx)(o,{})}}function r(){return(0,d.jsx)(l.Suspense,{fallback:(0,d.jsx)(p,{}),children:(0,d.jsx)(q,{})})}var s=c(12781);let t="force-dynamic",u={metadataBase:new URL("https://onlyrules.codes"),title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.",keywords:"AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy",authors:[{name:"OnlyRules Team"}],publisher:"OnlyRules",category:"Technology",classification:"Software Development Tools",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs.",type:"website",locale:"en_US",siteName:"OnlyRules"},twitter:{card:"summary_large_image",title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs."},alternates:{canonical:"https://onlyrules.codes"}},v={width:"device-width",initialScale:1,maximumScale:5,userScalable:!0,themeColor:[{media:"(prefers-color-scheme: light)",color:"hsl(0 0% 100%)"},{media:"(prefers-color-scheme: dark)",color:"hsl(240 10% 3.9%)"}]};async function w({children:a}){return await k(),(0,d.jsx)(e.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:(0,d.jsx)(g.QueryProvider,{children:(0,d.jsxs)(f.JotaiProvider,{children:[(0,d.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[(0,d.jsx)(r,{}),(0,d.jsx)("main",{className:"flex-1",children:a}),(0,d.jsx)("footer",{className:"border-t border-border",children:(0,d.jsx)("div",{className:"mobile-container py-6 xs:py-8",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4",children:[(0,d.jsx)("div",{className:"flex flex-col md:flex-row items-center gap-4",children:(0,d.jsx)("p",{className:"text-xs xs:text-sm text-center text-muted-foreground",children:"Built with ❤️ for the AI coding community."})}),(0,d.jsx)("div",{className:"flex flex-col md:flex-row items-center gap-4",children:(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,d.jsx)("a",{href:"/terms",className:"text-xs xs:text-sm text-primary hover:underline focus-visible-ring rounded touch-target",children:"Terms of Service"}),(0,d.jsx)("a",{href:"/privacy",className:"text-xs xs:text-sm text-primary hover:underline focus-visible-ring rounded touch-target",children:"Privacy Policy"}),(0,d.jsx)("a",{href:"https://toolsdk.ai/",target:"_blank",rel:"noopener noreferrer",className:"text-xs xs:text-sm text-primary hover:underline focus-visible-ring rounded touch-target",children:"ToolSDK.ai"})]})})]})})})]}),(0,d.jsx)(s.Toaster,{position:"top-center",toastOptions:{style:{fontSize:"14px"},className:"text-sm"}})]})})})}},19296:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,32526,23)),Promise.resolve().then(c.t.bind(c,30385,23)),Promise.resolve().then(c.t.bind(c,33737,23)),Promise.resolve().then(c.t.bind(c,1904,23)),Promise.resolve().then(c.t.bind(c,35856,23)),Promise.resolve().then(c.t.bind(c,55492,23)),Promise.resolve().then(c.t.bind(c,89082,23)),Promise.resolve().then(c.t.bind(c,45812,23)),Promise.resolve().then(c.bind(c,3220))},19374:()=>{},19959:(a,b,c)=>{var d={"./en/messages.js":[80271,271],"./zh-CN/messages.js":[47586,7586],"./zh-HK/messages.js":[13186,3186]};function e(a){if(!c.o(d,a))return Promise.resolve().then(()=>{var b=Error("Cannot find module '"+a+"'");throw b.code="MODULE_NOT_FOUND",b});var b=d[a],e=b[0];return c.e(b[1]).then(()=>c.t(e,23))}e.keys=()=>Object.keys(d),e.id=19959,a.exports=e},21160:(a,b,c)=>{"use strict";c.d(b,{ClientNavbar:()=>K});var d=c(13486),e=c(49989),f=c.n(e),g=c(60159),h=c(48961),i=c(44022),j=c(73120),k=c(92365),l=c(97490),m=c(57956),n=c(34606),o=c(77816),p=c(1039),q=c(62424),r=c(45977),s=c(93374),t=c(75370),u=c(73916),v=c(14788);let w={en:"English","zh-CN":"简体中文","zh-HK":"繁體中文"},x={en:"EN","zh-CN":"简","zh-HK":"繁"};function y({currentLocale:a}){let[b,c]=(0,g.useTransition)(),[e,f]=(0,g.useState)(a);return(0,d.jsxs)(v.Root,{value:e,onValueChange:a=>{f(a),c(()=>{document.cookie=`locale=${a};path=/;max-age=31536000`,window.location.reload()})},disabled:b,children:[(0,d.jsx)(v.Trigger,{className:"w-[60px] px-2 h-8 text-xs",placeholder:x[e]}),(0,d.jsx)(v.Content,{children:Object.entries(x).map(([a,b])=>(0,d.jsxs)(v.Item,{value:a,children:[(0,d.jsx)("span",{className:"text-xs",children:b}),(0,d.jsx)("span",{className:"ml-2 text-xs text-muted-foreground",children:w[a]})]},a))})]})}var z=c(36114),A=c(38785),B=c(7921),C=c(74439),D=c(83991),E=c(16219),F=c(37558),G=c(89307),H=c(81604);function I({open:a,onOpenChange:b}){let[c,e]=(0,z.fp)(G.in),[f,h]=(0,g.useState)(""),j=Object.entries(G.VG).filter(([a])=>!c.preferredIDEs.some(b=>b.type===a));return(0,d.jsx)(A.bL,{open:a,onOpenChange:b,children:(0,d.jsxs)(A.UC,{className:"max-w-2xl",children:[(0,d.jsxs)(A.hE,{className:"flex items-center gap-2",children:[(0,d.jsx)(n.A,{className:"h-5 w-5"}),"IDE Preferences"]}),(0,d.jsx)(A.VY,{children:"Manage your preferred IDEs for quick command generation. Set a default IDE and add your favorites."}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("h3",{className:"text-sm font-medium",children:"Add IDE"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(v.Root,{value:f,onValueChange:a=>h(a),children:[(0,d.jsx)(v.Trigger,{className:"flex-1",placeholder:"Select an IDE to add..."}),(0,d.jsx)(v.Content,{children:j.map(([a,b])=>(0,d.jsx)(v.Item,{value:a,children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:b.name}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:b.description})]})]})},a))})]}),(0,d.jsxs)(r.$,{onClick:()=>{if(!f)return;let a={id:`${f}-${Date.now()}`,name:G.VG[f].name,type:f,isDefault:0===c.preferredIDEs.length,addedAt:new Date().toISOString()};e({...c,preferredIDEs:[...c.preferredIDEs,a],defaultIDE:0===c.preferredIDEs.length?a.id:c.defaultIDE}),h(""),H.oR.success(`${G.VG[f].name} added to preferences`)},disabled:!f,size:"2",children:[(0,d.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"Add"]})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("h3",{className:"text-sm font-medium",children:["Preferred IDEs (",c.preferredIDEs.length,")"]}),0===c.preferredIDEs.length?(0,d.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,d.jsx)(i.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,d.jsx)("p",{children:"No preferred IDEs added yet"}),(0,d.jsx)("p",{className:"text-xs",children:"Add your favorite IDEs to generate quick commands"})]}):(0,d.jsx)("div",{className:"space-y-2",children:c.preferredIDEs.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"font-medium",children:a.name}),c.defaultIDE===a.id&&(0,d.jsx)(B.E,{variant:"soft",className:"text-xs",children:"Default"})]}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:G.VG[a.type].description})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(r.$,{variant:"ghost",size:"2",onClick:()=>(a=>{let b=c.preferredIDEs.find(b=>b.id===a);e({...c,defaultIDE:a}),b&&H.oR.success(`${b.name} set as default IDE`)})(a.id),disabled:c.defaultIDE===a.id,className:"h-8 w-8 p-0",children:c.defaultIDE===a.id?(0,d.jsx)(D.A,{className:"h-4 w-4 fill-current"}):(0,d.jsx)(E.A,{className:"h-4 w-4"})}),(0,d.jsx)(r.$,{variant:"ghost",size:"2",onClick:()=>(a=>{let b=c.preferredIDEs.find(b=>b.id===a),d=c.preferredIDEs.filter(b=>b.id!==a),f=c.defaultIDE;c.defaultIDE===a&&(f=d.length>0?d[0].id:void 0),e({preferredIDEs:d,defaultIDE:f}),b&&H.oR.success(`${b.name} removed from preferences`)})(a.id),className:"h-8 w-8 p-0 text-destructive hover:text-destructive",children:(0,d.jsx)(F.A,{className:"h-4 w-4"})})]})]},a.id))})]}),(0,d.jsxs)("div",{className:"text-xs text-muted-foreground bg-muted p-3 rounded-lg",children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"How it works:"}),(0,d.jsxs)("ul",{className:"space-y-1",children:[(0,d.jsx)("li",{children:"• Add your favorite IDEs to generate quick npx commands"}),(0,d.jsx)("li",{children:"• Set a default IDE for one-click command copying"}),(0,d.jsx)("li",{children:"• Commands will include the --target flag for your selected IDE"})]})]})]})]})})}function J({locale:a}){let{theme:b,setTheme:c}=(0,h.D)(),{data:e}=(0,u.wV)(),[v,w]=(0,g.useState)(!1),[x,z]=(0,g.useState)(!1),[A,B]=(0,g.useState)(!1),C=async()=>{await (0,u.CI)(),z(!1),B(!1)},D=()=>{w(!0),z(!1),B(!1)},E=()=>{z(!1),B(!1)},F=[{href:"/dashboard",label:"Dashboard"},{href:"/templates",label:"Templates"},{href:"/ides",label:"IDEs"},{href:"/tutorials",label:"Tutorials"},{href:"/docs",label:"Docs"},{href:"/shared",label:"Community"}];return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("nav",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:[(0,d.jsxs)("div",{className:"mobile-container flex h-14 xs:h-16 items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 xs:gap-8",children:[(0,d.jsxs)(f(),{href:"/",className:"flex items-center gap-2",onClick:E,children:[(0,d.jsx)(i.A,{className:"h-5 w-5 xs:h-6 xs:w-6 text-primary"}),(0,d.jsx)("span",{className:"font-bold text-lg xs:text-xl",children:"OnlyRules"})]}),(0,d.jsx)("nav",{className:"hidden md:flex items-center gap-6",children:F.map(a=>(0,d.jsx)(f(),{href:a.href,className:"text-sm font-medium transition-colors hover:text-primary",onClick:E,children:a.label},a.href))})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 xs:gap-4",children:[(0,d.jsxs)("div",{className:"hidden xs:flex items-center gap-2 xs:gap-3",children:[(0,d.jsx)(y,{currentLocale:a}),(0,d.jsx)(r.$,{variant:"ghost",size:"2",asChild:!0,className:"touch-target",children:(0,d.jsxs)(f(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"GitHub"})]})}),(0,d.jsxs)(r.$,{variant:"ghost",size:"2",onClick:()=>c("dark"===b?"light":"dark"),className:"touch-target",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,d.jsx)(l.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,d.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}),e?.user?(0,d.jsxs)(s.Root,{open:x,onOpenChange:z,children:[(0,d.jsx)(s.Trigger,{children:(0,d.jsx)(r.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full touch-target",children:(0,d.jsx)(t.Avatar,{className:"h-8 w-8",src:e.user.image||"",fallback:e.user.name?.charAt(0)||e.user.email?.charAt(0)||"U"})})}),(0,d.jsxs)(s.Content,{className:"w-56",align:"end",children:[(0,d.jsx)("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[e.user.name&&(0,d.jsx)("p",{className:"font-medium text-sm",children:e.user.name}),e.user.email&&(0,d.jsx)("p",{className:"w-[200px] truncate text-xs text-muted-foreground",children:e.user.email})]})}),(0,d.jsx)(s.Separator,{}),(0,d.jsx)(s.Item,{asChild:!0,children:(0,d.jsxs)(f(),{href:"/dashboard",onClick:E,className:"text-sm",children:[(0,d.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,d.jsxs)(s.Item,{onClick:D,className:"text-sm",children:[(0,d.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"IDE Preferences"]}),(0,d.jsx)(s.Item,{asChild:!0,children:(0,d.jsxs)(f(),{href:"/settings",onClick:E,className:"text-sm",children:[(0,d.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Settings"]})}),(0,d.jsx)(s.Separator,{}),(0,d.jsxs)(s.Item,{onClick:C,className:"text-sm",children:[(0,d.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Sign Out"]})]})]}):(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsx)(r.$,{variant:"ghost",size:"2",className:"touch-target",children:(0,d.jsx)(f(),{href:"/auth/signin",onClick:E,children:"Sign In"})})})]}),(0,d.jsx)(r.$,{variant:"ghost",size:"2",onClick:()=>{B(!A)},className:"xs:hidden touch-target","aria-label":"Toggle mobile menu",children:A?(0,d.jsx)(p.A,{className:"h-5 w-5"}):(0,d.jsx)(q.A,{className:"h-5 w-5"})})]})]}),A&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40 xs:hidden",onClick:()=>B(!1)}),(0,d.jsx)("div",{className:`
          fixed top-14 left-0 right-0 bg-background border-b shadow-lg z-50 xs:hidden
          transform transition-transform duration-300 ease-in-out
          ${A?"translate-y-0":"-translate-y-full"}
        `,children:(0,d.jsxs)("div",{className:"mobile-container py-4 space-y-4",children:[(0,d.jsx)("nav",{className:"space-y-3",children:F.map(a=>(0,d.jsx)(f(),{href:a.href,className:"block py-2 text-sm font-medium transition-colors hover:text-primary touch-target",onClick:E,children:a.label},a.href))}),(0,d.jsxs)("div",{className:"border-t pt-4 space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Language"}),(0,d.jsx)(y,{currentLocale:a})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Theme"}),(0,d.jsxs)(r.$,{variant:"ghost",size:"2",onClick:()=>c("dark"===b?"light":"dark"),className:"touch-target",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,d.jsx)(l.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,d.jsx)("span",{className:"ml-2 text-sm",children:"dark"===b?"Light":"Dark"})]})]}),(0,d.jsxs)(f(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target",onClick:E,children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),"GitHub"]}),e?.user?(0,d.jsxs)("div",{className:"border-t pt-4 space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 py-2",children:[(0,d.jsx)(t.Avatar,{className:"h-8 w-8",src:e.user.image||"",fallback:e.user.name?.charAt(0)||e.user.email?.charAt(0)||"U"}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[e.user.name&&(0,d.jsx)("p",{className:"font-medium text-sm truncate",children:e.user.name}),e.user.email&&(0,d.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:e.user.email})]})]}),(0,d.jsxs)(f(),{href:"/dashboard",onClick:E,className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"Dashboard"]}),(0,d.jsxs)("button",{onClick:D,className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target w-full text-left",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),"IDE Preferences"]}),(0,d.jsxs)(f(),{href:"/settings",onClick:E,className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),"Settings"]}),(0,d.jsxs)("button",{onClick:C,className:"flex items-center gap-3 py-2 text-sm font-medium text-destructive transition-colors hover:text-destructive/80 touch-target w-full text-left",children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),"Sign Out"]})]}):(0,d.jsx)("div",{className:"border-t pt-4",children:(0,d.jsx)(f(),{href:"/auth/signin",onClick:E,className:"block w-full text-center py-3 px-4 bg-primary text-primary-foreground rounded-lg font-medium text-sm touch-target",children:"Sign In"})})]})]})})]}),(0,d.jsx)(I,{open:v,onOpenChange:w})]})}function K({locale:a}){return(0,d.jsx)(J,{locale:a})}},21971:()=>{},34356:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(38828);function e({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{children:a})})}c(21971)},35464:(a,b,c)=>{Promise.resolve().then(c.bind(c,90206))},52548:(a,b,c)=>{"use strict";c.d(b,{ClientNavbar:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call ClientNavbar() from the server but ClientNavbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/layout/client-navbar.tsx","ClientNavbar")},53720:(a,b,c)=>{"use strict";c.d(b,{l:()=>g,q:()=>f});var d=c(38836),e=c(81604);let f=new d.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(a,b)=>!(b?.status>=400&&b?.status<500)&&a<3,refetchOnWindowFocus:!1,throwOnError:!1},mutations:{retry:!1,onError:a=>{console.error("Mutation error:",a);let b=a?.message||"An error occurred";e.oR.error(b)}}}}),g={rules:{all:["rules"],lists:()=>[...g.rules.all,"list"],list:a=>[...g.rules.lists(),a],details:()=>[...g.rules.all,"detail"],detail:a=>[...g.rules.details(),a],raw:a=>[...g.rules.all,"raw",a]},rulesets:{all:["rulesets"],lists:()=>[...g.rulesets.all,"list"],list:a=>[...g.rulesets.lists(),a],details:()=>[...g.rulesets.all,"detail"],detail:a=>[...g.rulesets.details(),a]},tags:{all:["tags"],lists:()=>[...g.tags.all,"list"]},user:{all:["user"],profile:a=>[...g.user.all,"profile",a]}}},59126:()=>{},73103:(a,b,c)=>{var d={"./en/messages.js":[62989,2989],"./zh-CN/messages.js":[12588,2588],"./zh-HK/messages.js":[99384,9384]};function e(a){if(!c.o(d,a))return Promise.resolve().then(()=>{var b=Error("Cannot find module '"+a+"'");throw b.code="MODULE_NOT_FOUND",b});var b=d[a],e=b[0];return c.e(b[1]).then(()=>c.t(e,23))}e.keys=()=>Object.keys(d),e.id=73103,a.exports=e},73916:(a,b,c)=>{"use strict";c.d(b,{CI:()=>f,Jv:()=>d,wV:()=>g});let{signIn:d,signUp:e,signOut:f,useSession:g,getSession:h}=(0,c(2992).MB)({baseURL:"https://onlyrules.codes"})},79104:(a,b,c)=>{"use strict";c.d(b,{QueryProvider:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/query-provider.tsx","QueryProvider")},80408:()=>{},84099:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>i});var d=c(13486),e=c(60159),f=c(48961),g=c(5924);function h({children:a}){let{theme:b,systemTheme:c}=(0,f.D)(),[h,i]=e.useState(!1);return(e.useEffect(()=>{i(!0)},[]),h)?(0,d.jsx)(g.Theme,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:("system"===b?c||"dark":b)||"dark",panelBackground:"translucent",hasBackground:!1,children:a}):(0,d.jsx)(g.Theme,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:"dark",panelBackground:"translucent",hasBackground:!1,children:a})}function i({children:a,...b}){return(0,d.jsx)(f.N,{defaultTheme:"dark",...b,children:(0,d.jsx)(h,{children:a})})}},87032:()=>{},87649:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx","ThemeProvider")},89307:(a,b,c)=>{"use strict";c.d(b,{VG:()=>f,in:()=>g});var d=c(49654),e=c(40056);let f={CURSOR:{name:"Cursor",description:"AI-powered code editor",command:"cursor"},AUGMENT:{name:"Augment",description:"AI coding assistant",command:"augment"},WINDSURF:{name:"Windsurf",description:"AI development environment",command:"windsurf"},CLAUDE:{name:"Claude",description:"Anthropic AI assistant",command:"claude"},GITHUB_COPILOT:{name:"GitHub Copilot",description:"AI pair programmer",command:"github-copilot"},GEMINI:{name:"Gemini",description:"Google AI assistant",command:"gemini"},OPENAI_CODEX:{name:"OpenAI Codex",description:"OpenAI code assistant",command:"openai-codex"},CLINE:{name:"Cline",description:"AI coding assistant",command:"cline"},JUNIE:{name:"Junie",description:"AI development tool",command:"junie"},TRAE:{name:"Trae",description:"AI coding companion",command:"trae"},LINGMA:{name:"Lingma",description:"AI programming assistant",command:"lingma"},KIRO:{name:"Kiro",description:"AI development environment",command:"kiro"},TENCENT_CODEBUDDY:{name:"Tencent CodeBuddy",description:"Tencent AI coding assistant",command:"tencent-codebuddy"},GENERAL:{name:"General",description:"General purpose IDE",command:"general"}};(0,d.eU)([]),(0,d.eU)([]),(0,d.eU)(null),(0,d.eU)(""),(0,d.eU)([]),(0,d.eU)("ALL"),(0,e.tG)("theme","system");let g=(0,e.tG)("ide-preferences",{preferredIDEs:[],defaultIDE:void 0});(0,d.eU)(a=>a(g).preferredIDEs,(a,b,c)=>{let d=a(g);b(g,{...d,preferredIDEs:c})}),(0,d.eU)(a=>{let b=a(g);return b.preferredIDEs.find(a=>a.id===b.defaultIDE)},(a,b,c)=>{let d=a(g);b(g,{...d,defaultIDE:c})})},89920:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,93824,23)),Promise.resolve().then(c.t.bind(c,69355,23)),Promise.resolve().then(c.t.bind(c,54439,23)),Promise.resolve().then(c.t.bind(c,94730,23)),Promise.resolve().then(c.t.bind(c,19774,23)),Promise.resolve().then(c.t.bind(c,53170,23)),Promise.resolve().then(c.t.bind(c,20968,23)),Promise.resolve().then(c.t.bind(c,78298,23)),Promise.resolve().then(c.t.bind(c,10282,23))},90206:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/error.tsx","default")},92403:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>g});var d=c(13486),e=c(48961),f=c(81604);let g=({...a})=>{let{theme:b="system"}=(0,e.D)();return(0,d.jsx)(f.l$,{theme:b,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...a})}},99016:(a,b,c)=>{Promise.resolve().then(c.bind(c,176))},99023:(a,b,c)=>{"use strict";c.d(b,{JotaiProvider:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call JotaiProvider() from the server but JotaiProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/jotai-provider.tsx","JotaiProvider")}};