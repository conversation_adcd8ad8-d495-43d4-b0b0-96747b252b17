exports.id=4623,exports.ids=[4623],exports.modules={28929:(a,b,c)=>{Promise.resolve().then(c.bind(c,84588))},42081:(a,b,c)=>{Promise.resolve().then(c.bind(c,83526))},65391:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(38828);let e={title:{template:"%s | OnlyRules - AI Code IDE Integration",default:"Supported IDEs | OnlyRules"},description:"Learn how to integrate OnlyRules with your favorite AI-powered code IDE. Step-by-step tutorials for Cursor, VS Code, WebStorm, and more.",keywords:"AI IDE integration, OnlyRules tutorial, Cursor AI, VS Code AI, WebStorm AI, IDE setup, AI coding assistant, prompt rules, code generation",openGraph:{title:"Supported IDEs - OnlyRules Integration",description:"Complete guides for integrating OnlyRules with popular AI-powered IDEs",type:"website"}};function f({children:a}){return(0,d.jsx)("div",{className:"min-h-screen",children:a})}},83526:(a,b,c)=>{"use strict";c.d(b,{default:()=>v});var d=c(13486),e=c(60159),f=c(7921),g=c(45977),h=c(92499),i=c(53883),j=c(5229),k=c(31461),l=c(82376),m=c(5447),n=c(79438),o=c(38782),p=c(57660),q=c(34606),r=c(39138),s=c(9575),t=c(72513),u=c(82319);function v({ide:a,installation:b,integration:c,features:v,examples:w,tips:x}){let[y,z]=(0,e.useState)(null),A=(a,b)=>{navigator.clipboard.writeText(a),z(b),setTimeout(()=>z(null),2e3)};return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,d.jsxs)("div",{className:"mb-12",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,d.jsx)("div",{className:`p-4 rounded-lg ${a.color} bg-opacity-10`,children:(0,d.jsx)("span",{className:"text-5xl",children:a.icon})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-4xl font-bold mb-2",children:[a.name," Integration Guide"]}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground",children:a.description})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)(f.E,{variant:"outline",children:["Version ",a.version]}),(0,d.jsx)("a",{href:a.website,target:"_blank",rel:"noopener noreferrer",children:(0,d.jsxs)(g.$,{variant:"outline",size:"2",children:["Visit Official Website",(0,d.jsx)(k.A,{className:"ml-2 h-4 w-4"})]})})]})]}),(0,d.jsxs)(h.Root,{className:"mb-8",children:[(0,d.jsx)(h.Icon,{children:(0,d.jsx)(l.A,{className:"h-4 w-4"})}),(0,d.jsxs)(h.Text,{children:[(0,d.jsx)("strong",{children:"Quick Start:"})," Get up and running with ",a.name," + OnlyRules in just a few minutes. Follow our step-by-step guide below to enhance your AI coding experience."]})]}),(0,d.jsxs)(i.bL,{defaultValue:"installation",className:"space-y-8",children:[(0,d.jsxs)(i.B8,{className:"grid w-full grid-cols-4 lg:w-[600px]",children:[(0,d.jsx)(i.l9,{value:"installation",children:"Installation"}),(0,d.jsx)(i.l9,{value:"integration",children:"Integration"}),(0,d.jsx)(i.l9,{value:"examples",children:"Examples"}),(0,d.jsx)(i.l9,{value:"tips",children:"Best Practices"})]}),(0,d.jsx)(i.UC,{value:"installation",className:"space-y-6",children:(0,d.jsxs)(j.Z,{children:[(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 font-semibold text-lg",children:[(0,d.jsx)(m.A,{className:"h-5 w-5"}),"Installing ",a.name]}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Follow these steps to install and set up ",a.name," on your system"]})]}),(0,d.jsx)("div",{className:"space-y-6",children:b.steps.map((b,c)=>(0,d.jsx)("div",{className:"space-y-3",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:`mt-1 h-8 w-8 rounded-full ${a.color} bg-opacity-20 flex items-center justify-center flex-shrink-0`,children:(0,d.jsx)("span",{className:"text-sm font-semibold",children:c+1})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"font-semibold mb-1",children:b.title}),(0,d.jsx)("p",{className:"text-muted-foreground",children:b.description}),b.command&&(0,d.jsxs)("div",{className:"mt-3 relative",children:[(0,d.jsx)("pre",{className:"bg-muted p-4 rounded-lg overflow-x-auto",children:(0,d.jsx)("code",{children:b.command})}),(0,d.jsx)(g.$,{size:"1",variant:"ghost",className:"absolute top-2 right-2",onClick:()=>A(b.command||"",c),children:y===c?(0,d.jsx)(n.A,{className:"h-4 w-4 text-green-500"}):(0,d.jsx)(o.A,{className:"h-4 w-4"})})]}),b.note&&(0,d.jsxs)(h.Root,{className:"mt-3",children:[(0,d.jsx)(h.Icon,{children:(0,d.jsx)(p.A,{className:"h-4 w-4"})}),(0,d.jsx)(h.Text,{children:b.note})]})]})]})},c))})]})}),(0,d.jsxs)(i.UC,{value:"integration",className:"space-y-6",children:[(0,d.jsxs)(j.Z,{children:[(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 font-semibold text-lg",children:[(0,d.jsx)(q.A,{className:"h-5 w-5"}),"Integrating OnlyRules with ",a.name]}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"Connect OnlyRules to enhance your AI coding experience"})]}),(0,d.jsx)("div",{className:"space-y-6",children:c.steps.map((b,c)=>(0,d.jsx)("div",{className:"space-y-3",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:`mt-1 h-8 w-8 rounded-full ${a.color} bg-opacity-20 flex items-center justify-center flex-shrink-0`,children:(0,d.jsx)("span",{className:"text-sm font-semibold",children:c+1})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"font-semibold mb-1",children:b.title}),(0,d.jsx)("p",{className:"text-muted-foreground",children:b.description}),b.code&&(0,d.jsxs)("div",{className:"mt-3 relative",children:[(0,d.jsx)("pre",{className:"bg-muted p-4 rounded-lg overflow-x-auto",children:(0,d.jsx)("code",{children:b.code})}),(0,d.jsx)(g.$,{size:"1",variant:"ghost",className:"absolute top-2 right-2",onClick:()=>A(b.code||"",c+100),children:y===c+100?(0,d.jsx)(n.A,{className:"h-4 w-4 text-green-500"}):(0,d.jsx)(o.A,{className:"h-4 w-4"})})]})]})]})},c))})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-3 gap-4",children:v.map((a,b)=>(0,d.jsx)(j.Z,{children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 font-semibold text-lg",children:[a.icon,a.title]}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.description})]})},b))})]}),(0,d.jsx)(i.UC,{value:"examples",className:"space-y-6",children:(0,d.jsxs)(j.Z,{children:[(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 font-semibold text-lg",children:[(0,d.jsx)(r.A,{className:"h-5 w-5"}),"Example Prompts for ",a.name]}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Try these optimized prompts to get the most out of ",a.name," with OnlyRules"]})]}),(0,d.jsx)("div",{className:"space-y-6",children:w.map((a,b)=>(0,d.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,d.jsx)("h4",{className:"font-semibold",children:a.title}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:a.description}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("pre",{className:"bg-muted p-4 rounded-lg overflow-x-auto text-sm",children:(0,d.jsx)("code",{children:a.prompt})}),(0,d.jsx)(g.$,{size:"1",variant:"ghost",className:"absolute top-2 right-2",onClick:()=>A(a.prompt,b+200),children:y===b+200?(0,d.jsx)(n.A,{className:"h-4 w-4 text-green-500"}):(0,d.jsx)(o.A,{className:"h-4 w-4"})})]}),a.result&&(0,d.jsxs)("div",{className:"mt-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-2",children:"Expected Result:"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:a.result})]})]},b))})]})}),(0,d.jsxs)(i.UC,{value:"tips",className:"space-y-6",children:[(0,d.jsxs)(j.Z,{children:[(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 font-semibold text-lg",children:[(0,d.jsx)(s.A,{className:"h-5 w-5"}),"Best Practices for ",a.name]}),(0,d.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Tips and tricks to maximize your productivity with ",a.name," and OnlyRules"]})]}),(0,d.jsx)("div",{children:(0,d.jsx)("ul",{className:"space-y-4",children:x.map((b,c)=>(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:`mt-0.5 h-6 w-6 rounded-full ${a.color} bg-opacity-20 flex items-center justify-center flex-shrink-0`,children:(0,d.jsx)("span",{className:"text-xs",children:"✓"})}),(0,d.jsx)("span",{className:"text-muted-foreground",children:b})]},c))})})]}),(0,d.jsxs)(j.Z,{children:[(0,d.jsx)("div",{className:"space-y-2 mb-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-2 font-semibold text-lg",children:[(0,d.jsx)(t.A,{className:"h-5 w-5"}),"Additional Resources"]})}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("a",{href:"/templates",className:"flex items-center gap-2 text-primary hover:underline",children:[(0,d.jsx)(u.A,{className:"h-4 w-4"}),"Browse ",a.name," prompt templates"]}),(0,d.jsxs)("a",{href:"/tutorials",className:"flex items-center gap-2 text-primary hover:underline",children:[(0,d.jsx)(s.A,{className:"h-4 w-4"}),"View advanced tutorials"]}),(0,d.jsxs)("a",{href:"/dashboard",className:"flex items-center gap-2 text-primary hover:underline",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"Manage your rules"]})]})]})]})]})]})}},84588:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/ide-page-template.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/ide-page-template.tsx","default")}};