"use strict";exports.id=7649,exports.ids=[7649],exports.modules={21693:(a,b,c)=>{c.d(b,{B:()=>n});var d=c(13486),e=c(48961),f=c(39798),g=c(59006),h=c(2708),i=c(91878),j=c(74415),k=c(82586),l=c(95135),m=c(12888);function n({value:a,onChange:b,placeholder:c,className:n,language:o="javascript",readOnly:p=!1}){let{theme:q}=(0,e.D)(),r=[(()=>{switch(o){case"markdown":return(0,h.wD)();case"json":return(0,i.Pq)();case"xml":return(0,j._n)();default:return(0,g.Q2)()}})(),l.Lz.theme({"&":{fontSize:"14px"},".cm-content":{padding:"16px",minHeight:"200px"},".cm-focused":{outline:"none"},".cm-editor":{borderRadius:"8px"},"&.cm-editor.cm-readonly .cm-content":{backgroundColor:p?"dark"===q?"#1a1a1a":"#f8f9fa":"inherit"}})];return p&&r.push(m.$t.readOnly.of(!0)),(0,d.jsx)("div",{className:n,children:(0,d.jsx)(f.Ay,{value:a,onChange:b?a=>b(a):void 0,placeholder:c,theme:"dark"===q?k.bM:void 0,extensions:r,readOnly:p,basicSetup:{lineNumbers:!0,foldGutter:!0,dropCursor:!1,allowMultipleSelections:!1,indentOnInput:!p,bracketMatching:!0,closeBrackets:!p,autocompletion:!p,highlightSelectionMatches:!1}})})}},57649:(a,b,c)=>{c.d(b,{z:()=>H});var d=c(13486),e=c(60159),f=c(36114),g=c(5229),h=c(93374),i=c(45977),j=c(7921),k=c(38785),l=c(3605),m=c(77060),n=c(42726),o=c(38782),p=c(82319),q=c(83991),r=c(44022),s=c(9532),t=c(5447),u=c(23152),v=c(72513),w=c(77026),x=c(70393),y=c(89307);function z(a,b){let c=y.VG[b].command;return`npx onlyrules -f "${a}" --target ${c}`}function A(a){return`npx onlyrules -f "${a}"`}function B(a,b){return[...a].sort((a,c)=>a.id===b?-1:c.id===b?1:a.name.localeCompare(c.name))}async function C(a){try{return await navigator.clipboard.writeText(a),!0}catch(a){return console.error("Failed to copy to clipboard:",a),!1}}var D=c(21693),E=c(81604),F=c(49989),G=c.n(F);function H({rule:a,onEdit:b,onDelete:c,isOwner:F=!1}){let[H,I]=(0,e.useState)(!1),[J,K]=(0,e.useState)(!1),[L]=(0,f.fp)(y.in),M=async()=>{await navigator.clipboard.writeText(a.content),E.oR.success("Rule content copied to clipboard")},N=async()=>{if("PUBLIC"!==a.visibility)return void E.oR.error("Only public rules can be used with npx command");let b=A(`${window.location.origin}/api/rules/raw?id=${a.id}`);await C(b)?(K(!0),E.oR.success("CLI command copied to clipboard"),setTimeout(()=>K(!1),2e3)):E.oR.error("Failed to copy command to clipboard")},O=async b=>{if("PUBLIC"!==a.visibility)return void E.oR.error("Only public rules can be used with npx command");let c=L.preferredIDEs.find(a=>a.id===b);if(!c)return;let d=z(`${window.location.origin}/api/rules/raw?id=${a.id}`,c.type);await C(d)?E.oR.success(`${c.name} command copied to clipboard`):E.oR.error("Failed to copy command to clipboard")},P=async()=>{if("PUBLIC"!==a.visibility)return void E.oR.error("Only public rules can be used with npx command");let b=L.preferredIDEs.find(a=>a.id===L.defaultIDE);if(!b)return N();let c=z(`${window.location.origin}/api/rules/raw?id=${a.id}`,b.type);await C(c)?(K(!0),E.oR.success(`${b.name} command copied to clipboard`),setTimeout(()=>K(!1),2e3)):E.oR.error("Failed to copy command to clipboard")},Q=async()=>{if("PUBLIC"===a.visibility){let b=`${window.location.origin}/rules/${a.id}`;await navigator.clipboard.writeText(b),E.oR.success("Share link copied to clipboard")}else if(a.shareToken){let b=`${window.location.origin}/shared/${a.shareToken}`;await navigator.clipboard.writeText(b),E.oR.success("Share link copied to clipboard")}},R=async()=>{if("PUBLIC"!==a.visibility)return void E.oR.error("Only public rules can be downloaded as MDX");try{let b=await fetch(`/api/rules/download?id=${a.id}`);if(!b.ok)throw Error("Failed to download rule");let c=await b.blob(),d=URL.createObjectURL(c),e=document.createElement("a");e.href=d,e.download=`${a.title.toLowerCase().replace(/\s+/g,"-")}.mdx`,document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d),E.oR.success("Rule downloaded as MDX")}catch(a){E.oR.error("Failed to download rule as MDX")}};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(g.Z,{className:"group hover:shadow-md transition-all duration-200 mobile-card",children:[(0,d.jsx)("div",{className:"pb-3",children:(0,d.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,d.jsxs)("div",{className:"space-y-2 flex-1 min-w-0",children:["PUBLIC"===a.visibility?(0,d.jsx)(G(),{href:`/rules/${a.id}`,className:"touch-target",children:(0,d.jsxs)("div",{className:"text-base xs:text-lg font-semibold cursor-pointer hover:text-primary transition-colors inline-flex items-center gap-1",children:[(0,d.jsx)("span",{className:"truncate",children:a.title}),(0,d.jsx)(l.A,{className:"h-3 w-3 opacity-0 group-hover:opacity-50 flex-shrink-0"})]})}):(0,d.jsx)("div",{className:"text-base xs:text-lg font-semibold cursor-pointer hover:text-primary transition-colors touch-target truncate",onClick:()=>I(!0),children:a.title}),a.description&&(0,d.jsx)("p",{className:"text-xs xs:text-sm text-muted-foreground line-clamp-2 leading-relaxed",children:a.description})]}),(0,d.jsxs)(h.Root,{children:[(0,d.jsx)(h.Trigger,{children:(0,d.jsx)(i.$,{variant:"ghost",size:"1",className:"opacity-60 xs:opacity-0 xs:group-hover:opacity-100 transition-opacity touch-target flex-shrink-0",children:(0,d.jsx)(m.A,{className:"h-4 w-4"})})}),(0,d.jsxs)(h.Content,{align:"end",children:[(0,d.jsxs)(h.Item,{onClick:()=>I(!0),children:[(0,d.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"View"]}),"PUBLIC"===a.visibility&&(0,d.jsx)(h.Item,{asChild:!0,children:(0,d.jsxs)(G(),{href:`/rules/${a.id}`,children:[(0,d.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Open Rule Page"]})}),(0,d.jsxs)(h.Item,{onClick:M,children:[(0,d.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Copy Content"]}),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(h.Sub,{children:[(0,d.jsxs)(h.SubTrigger,{children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Copy CLI Command"]}),(0,d.jsxs)(h.SubContent,{children:[L.defaultIDE&&L.preferredIDEs.length>0&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(h.Item,{onClick:P,children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4 fill-current"}),L.preferredIDEs.find(a=>a.id===L.defaultIDE)?.name," (Default)"]}),(0,d.jsx)(h.Separator,{})]}),(0,d.jsxs)(h.Item,{onClick:N,children:[(0,d.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Basic Command"]}),L.preferredIDEs.length>0&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h.Separator,{}),B(L.preferredIDEs,L.defaultIDE).filter(a=>a.id!==L.defaultIDE).map(a=>(0,d.jsxs)(h.Item,{onClick:()=>O(a.id),children:[(0,d.jsx)(r.A,{className:"mr-2 h-4 w-4"}),a.name]},a.id))]}),0===L.preferredIDEs.length&&(0,d.jsx)("div",{className:"px-2 py-1.5 text-xs text-muted-foreground",children:"Add IDE preferences in settings for quick commands"})]})]}),(0,d.jsxs)(h.Item,{onClick:Q,children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Share"]})]}),(0,d.jsxs)(h.Sub,{children:[(0,d.jsxs)(h.SubTrigger,{children:[(0,d.jsx)(t.A,{className:"mr-2 h-4 w-4"}),"Download"]}),(0,d.jsxs)(h.SubContent,{children:[(0,d.jsxs)(h.Item,{onClick:()=>{let b=new Blob([JSON.stringify({title:a.title,description:a.description,content:a.content,ideType:a.ideType,tags:a.tags.map(a=>a.tag.name)},null,2)],{type:"application/json"}),c=URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download=`${a.title.toLowerCase().replace(/\s+/g,"-")}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(c),E.oR.success("Rule downloaded as JSON")},children:[(0,d.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"JSON"]}),(0,d.jsxs)(h.Item,{onClick:R,children:[(0,d.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"MDX"]})]})]}),F&&b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h.Separator,{}),(0,d.jsxs)(h.Item,{onClick:()=>b(a),children:[(0,d.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Edit"]})]}),F&&c&&(0,d.jsxs)(h.Item,{onClick:()=>c(a.id),className:"text-destructive",children:[(0,d.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]})}),(0,d.jsxs)("div",{className:"space-y-3 xs:space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsxs)(j.E,{variant:"soft",className:`${(a=>{switch(a){case"CURSOR":return"bg-blue-500";case"AUGMENT":return"bg-green-500";case"WINDSURF":return"bg-purple-500";case"CLAUDE":return"bg-orange-500";case"GITHUB_COPILOT":return"bg-gray-800";case"GEMINI":return"bg-indigo-500";case"OPENAI_CODEX":return"bg-teal-500";case"CLINE":return"bg-pink-500";case"JUNIE":return"bg-yellow-500";case"TRAE":return"bg-red-500";case"LINGMA":return"bg-cyan-500";case"KIRO":return"bg-emerald-500";case"TENCENT_CODEBUDDY":return"bg-violet-500";default:return"bg-gray-500"}})(a.ideType)} text-white text-xs xs:text-sm`,children:[(0,d.jsx)(r.A,{className:"mr-1 h-3 w-3"}),a.ideType]}),"PUBLIC"===a.visibility&&(0,d.jsx)(j.E,{variant:"outline",className:"text-xs xs:text-sm",children:"Public"})]}),a.tags.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-1 xs:gap-2",children:[a.tags.slice(0,3).map(a=>(0,d.jsx)(j.E,{variant:"outline",style:{borderColor:a.tag.color},className:"text-xs px-2 py-1",children:a.tag.name},a.tag.id)),a.tags.length>3&&(0,d.jsxs)(j.E,{variant:"outline",className:"text-xs px-2 py-1 text-muted-foreground",children:["+",a.tags.length-3," more"]})]}),(0,d.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated ",new Date(a.updatedAt).toLocaleDateString()]})]})]}),(0,d.jsx)(k.bL,{open:H,onOpenChange:I,children:(0,d.jsxs)(k.UC,{className:"max-w-4xl max-h-[90vh] w-[95vw] xs:w-[90vw] overflow-hidden",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(k.hE,{className:"text-lg xs:text-xl truncate",children:a.title}),a.description&&(0,d.jsx)(k.VY,{className:"text-sm xs:text-base",children:a.description})]}),(0,d.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,d.jsx)(D.B,{value:a.content,onChange:()=>{},className:"h-[50vh] xs:h-[60vh]"})}),"PUBLIC"===a.visibility&&(0,d.jsxs)("div",{className:"mt-4 space-y-3",children:[L.defaultIDE&&L.preferredIDEs.length>0&&(0,d.jsx)("div",{className:"p-3 bg-primary/5 border border-primary/20 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsxs)("p",{className:"text-sm font-medium flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 fill-current text-primary"}),L.preferredIDEs.find(a=>a.id===L.defaultIDE)?.name," (Default):"]}),(0,d.jsx)("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:z(`${window.location.origin}/api/rules/raw?id=${a.id}`,L.preferredIDEs.find(a=>a.id===L.defaultIDE)?.type||"GENERAL")})]}),(0,d.jsxs)(i.$,{size:"1",variant:"outline",onClick:P,className:"ml-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Copy"]})]})}),(0,d.jsx)("div",{className:"p-3 bg-muted rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Basic CLI Command:"}),(0,d.jsx)("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:A(`${window.location.origin}/api/rules/raw?id=${a.id}`)})]}),(0,d.jsxs)(i.$,{size:"1",variant:"outline",onClick:N,className:"ml-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),J?"Copied!":"Copy"]})]})}),L.preferredIDEs.length>0&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("p",{className:"text-sm font-medium",children:"Other IDE Commands:"}),(0,d.jsx)("div",{className:"grid gap-2",children:B(L.preferredIDEs,L.defaultIDE).filter(a=>a.id!==L.defaultIDE).map(b=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted/50 rounded text-xs",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[(0,d.jsx)(r.A,{className:"h-3 w-3 flex-shrink-0"}),(0,d.jsxs)("span",{className:"font-medium flex-shrink-0",children:[b.name,":"]}),(0,d.jsx)("code",{className:"bg-background px-1 py-0.5 rounded truncate",children:z(`${window.location.origin}/api/rules/raw?id=${a.id}`,b.type)})]}),(0,d.jsx)(i.$,{size:"1",variant:"ghost",onClick:()=>O(b.id),className:"ml-2 h-6 w-6 p-0",children:(0,d.jsx)(o.A,{className:"h-3 w-3"})})]},b.id))})]})]})]})})]})}}};