(()=>{var a={};a.id=4255,a.ids=[4255],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2984:(a,b,c)=>{"use strict";var d=c(860);c.o(d,"useParams")&&c.d(b,{useParams:function(){return d.useParams}}),c.o(d,"usePathname")&&c.d(b,{usePathname:function(){return d.usePathname}}),c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3605:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(84667).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},3898:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(24332),e=c(48819),f=c(93949),g=c(98730),h=c(88996),i=c(16318),j=c(3093),k=c(36748),l=c(98190),m=c(53904),n=c(47735),o=c(20611),p=c(22512),q=c(261),r=c(13863),s=c(8748),t=c(26713),u=c(65262),v=c(97779),w=c(5303),x=c(66704),y=c(67656),z=c(3072),A=c(86439),B=c(93824),C=c.n(B),D=c(97540),E=c(49005),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(main)",{children:["rules",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,16930)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/rules/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,17217)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,34356)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,90206)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,10584)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,93824,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/rules/[id]/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(main)/rules/[id]/page",pathname:"/rules/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(main)/rules/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},4038:(a,b,c)=>{Promise.resolve().then(c.bind(c,72382))},5229:(a,b,c)=>{"use strict";c.d(b,{Z:()=>j});var d=c(60159),e=c(86135),f=c(90691);let g={...c(23831).f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var h=c(87160),i=c(91683);let j=d.forwardRef((a,b)=>{let{asChild:c,className:j,...k}=(0,h.o)(a,g,i.y),l=c?f.bL:"div";return d.createElement(l,{ref:b,...k,className:e("rt-reset","rt-BaseCard","rt-Card",j)})});j.displayName="Card"},5447:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(84667).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9532:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(84667).A)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16930:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,dynamic:()=>e});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/rules/[id]/page.tsx","dynamic"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/rules/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/rules/[id]/page.tsx","default")},18692:(a,b,c)=>{"use strict";c.d(b,{El:()=>k,fs:()=>j,qT:()=>i});var d=c(21482),e=c(84364),f=c(53720);c(81604);let g=async a=>{let b=await fetch(`/api/rules/${a}`);if(!b.ok)throw{message:404===b.status?"Rule not found":403===b.status?"You don't have permission to view this rule":"Failed to load rule",status:b.status};return b.json()},h=async(a={})=>{let b=new URLSearchParams;a.search&&b.set("search",a.search),a.tags?.length&&b.set("tags",a.tags.join(",")),a.ideType&&b.set("ideType",a.ideType),a.visibility&&b.set("visibility",a.visibility),a.page&&b.set("page",a.page.toString()),a.limit&&b.set("limit",a.limit.toString());let c=await fetch(`/api/rules?${b}`);if(!c.ok)throw Error("Failed to fetch rules");let d=await c.json();return{rules:Array.isArray(d)?d:d.rules||[],totalCount:d.totalCount||d.length||0,totalPages:d.totalPages||1,currentPage:d.currentPage||1}},i=a=>(0,d.I)({queryKey:f.l.rules.detail(a),queryFn:()=>g(a),enabled:!!a,staleTime:3e5,retry:(a,b)=>b?.status!==404&&b?.status!==403&&a<3}),j=(a={})=>(0,d.I)({queryKey:f.l.rules.list(a),queryFn:()=>h(a),staleTime:12e4,keepPreviousData:!0}),k=()=>{let a=(0,e.jE)();return()=>{a.invalidateQueries({queryKey:f.l.rules.all})}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35582:(a,b,c)=>{Promise.resolve().then(c.bind(c,16930))},38782:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(84667).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72382:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s,dynamic:()=>r});var d=c(13486),e=c(2984),f=c(45977),g=c(5229),h=c(7921);let i=(0,c(84667).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var j=c(38782),k=c(9532),l=c(5447),m=c(3605),n=c(81604),o=c(49989),p=c.n(o),q=c(18692);let r="force-dynamic";function s(){let a=(0,e.useParams)().id,{data:b,isLoading:c,error:o,isError:r}=(0,q.qT)(a),s=async()=>{if(b)try{await navigator.clipboard.writeText(b.content),n.oR.success("Rule content copied to clipboard")}catch(a){n.oR.error("Failed to copy content")}},t=async()=>{try{let b=`${window.location.origin}/r/${a}`;await navigator.clipboard.writeText(b),n.oR.success("Rule link copied to clipboard")}catch(a){n.oR.error("Failed to copy link")}};if(c)return(0,d.jsx)("div",{className:"container max-w-4xl py-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Loading Rule..."}),(0,d.jsxs)("p",{className:"text-muted-foreground",children:["Rule ID: ",a]}),(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsx)("div",{className:"animate-pulse",children:(0,d.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto"})})})]})});if(r||!b){let a=o instanceof Error?o.message:"Rule not found";return(0,d.jsx)("div",{className:"container max-w-4xl py-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold mb-4 text-red-600",children:"Error"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:a}),(0,d.jsx)(f.$,{variant:"outline",asChild:!0,children:(0,d.jsxs)(p(),{href:"/rules",children:[(0,d.jsx)(i,{className:"mr-2 h-4 w-4"}),"Back to Rules"]})})]})})}return(0,d.jsxs)("div",{className:"container max-w-4xl py-8 space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,d.jsx)(f.$,{variant:"ghost",size:"1",asChild:!0,children:(0,d.jsx)(p(),{href:"/rules",children:(0,d.jsx)(i,{className:"h-4 w-4"})})}),(0,d.jsx)("span",{children:"Rules"})]}),(0,d.jsx)("h1",{className:"text-3xl font-bold",children:b.title}),b.description&&(0,d.jsx)("p",{className:"text-lg text-muted-foreground",children:b.description})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(f.$,{variant:"outline",size:"2",onClick:s,children:[(0,d.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Copy"]}),(0,d.jsxs)(f.$,{variant:"outline",size:"2",onClick:t,children:[(0,d.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Share"]}),(0,d.jsxs)(f.$,{variant:"outline",size:"2",onClick:()=>{if(!b)return;let a=new Blob([b.content],{type:"text/plain"}),c=URL.createObjectURL(a),d=document.createElement("a");d.href=c,d.download=`${b.title.replace(/[^a-z0-9]/gi,"_").toLowerCase()}.md`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(c),n.oR.success("Rule downloaded")},children:[(0,d.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Download"]})]})]}),(0,d.jsxs)(g.Z,{className:"p-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"IDE:"}),(0,d.jsx)(h.E,{variant:"outline",children:b.ideType})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Visibility:"}),(0,d.jsx)(h.E,{variant:"PUBLIC"===b.visibility?"solid":"outline",children:b.visibility})]}),b.user&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Author:"}),(0,d.jsx)("span",{className:"text-sm",children:b.user.name||b.user.email})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Created:"}),(0,d.jsx)("span",{className:"text-sm",children:new Date(b.createdAt).toLocaleDateString()})]})]}),b.tags&&b.tags.length>0&&(0,d.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Tags:"}),b.tags.map(a=>(0,d.jsx)(h.E,{variant:"soft",style:{backgroundColor:a.tag.color+"20",color:a.tag.color},children:a.tag.name},a.tag.id))]})})]}),(0,d.jsx)(g.Z,{className:"p-6",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"Rule Content"}),(0,d.jsx)(f.$,{variant:"ghost",size:"1",onClick:s,children:(0,d.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,d.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border",children:(0,d.jsx)("pre",{className:"whitespace-pre-wrap text-sm font-mono overflow-x-auto",children:b.content})})]})}),"PUBLIC"===b.visibility&&(0,d.jsx)(g.Z,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium",children:"Raw Data Access"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Access the raw rule content directly via API"})]}),(0,d.jsx)(f.$,{variant:"outline",size:"2",asChild:!0,children:(0,d.jsxs)(p(),{href:`/api/rules/raw?id=${b.id}`,target:"_blank",children:[(0,d.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Raw Data"]})})]})})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,5208,3512,4544,1482,709],()=>b(b.s=3898));module.exports=c})();