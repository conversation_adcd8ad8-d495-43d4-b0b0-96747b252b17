"use strict";(()=>{var a={};a.id=3362,a.ids=[3362],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28140:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(24332),e=c(48819),f=c(93949),g=c(98730),h=c(88996),i=c(16318),j=c(3093),k=c(36748),l=c(98190),m=c(53904),n=c(47735),o=c(20611),p=c(22512),q=c(261),r=c(13863),s=c(8748),t=c(26713),u=c(65262),v=c(97779),w=c(5303),x=c(66704),y=c(67656),z=c(3072),A=c(86439),B=c(93824),C=c.n(B),D=c(97540),E=c(49005),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(main)",{children:["(static)",{children:["privacy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,46139)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/privacy/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,91491)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,17217)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,34356)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,90206)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,10584)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,93824,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(main)/(static)/privacy/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(main)/(static)/privacy/page",pathname:"/privacy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(main)/(static)/privacy/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46139:(a,b,c)=>{c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(38828);let e={title:"Privacy Policy - OnlyRules",description:"Privacy Policy for OnlyRules - AI Prompt Management Platform",robots:"index, follow"};function f(){return(0,d.jsx)("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:(0,d.jsxs)("div",{className:"prose prose-gray dark:prose-invert max-w-none",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Privacy Policy"}),(0,d.jsxs)("p",{className:"text-muted-foreground mb-6",children:[(0,d.jsx)("strong",{children:"Last updated:"})," ",new Date().toLocaleDateString()]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"1. Introduction"}),(0,d.jsx)("p",{children:'OnlyRules ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website and use our services.'})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"2. Information We Collect"}),(0,d.jsx)("h3",{className:"text-xl font-medium mb-3",children:"2.1 Personal Information"}),(0,d.jsx)("p",{children:"We may collect personal information that you voluntarily provide, including:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"Name and email address (when creating an account)"}),(0,d.jsx)("li",{children:"Profile information and preferences"}),(0,d.jsx)("li",{children:"Content you create and share (AI prompt rules, templates)"}),(0,d.jsx)("li",{children:"Communications with us (support requests, feedback)"})]}),(0,d.jsx)("h3",{className:"text-xl font-medium mb-3 mt-6",children:"2.2 Automatically Collected Information"}),(0,d.jsx)("p",{children:"We automatically collect certain information when you visit our website:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"IP address and location data"}),(0,d.jsx)("li",{children:"Browser type and version"}),(0,d.jsx)("li",{children:"Device information (operating system, screen resolution)"}),(0,d.jsx)("li",{children:"Usage data (pages visited, time spent, clicks)"}),(0,d.jsx)("li",{children:"Cookies and similar tracking technologies"})]}),(0,d.jsx)("h3",{className:"text-xl font-medium mb-3 mt-6",children:"2.3 Analytics Information"}),(0,d.jsx)("p",{children:"We use Google Analytics to understand how users interact with our website. This includes:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"Page views and session duration"}),(0,d.jsx)("li",{children:"User demographics and interests"}),(0,d.jsx)("li",{children:"Acquisition channels and referral sources"}),(0,d.jsx)("li",{children:"Site performance and error tracking"})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"3. How We Use Your Information"}),(0,d.jsx)("p",{children:"We use the collected information for the following purposes:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"Provide, operate, and maintain our services"}),(0,d.jsx)("li",{children:"Improve and personalize user experience"}),(0,d.jsx)("li",{children:"Process transactions and manage user accounts"}),(0,d.jsx)("li",{children:"Send administrative and promotional communications"}),(0,d.jsx)("li",{children:"Analyze usage patterns and optimize performance"}),(0,d.jsx)("li",{children:"Detect, prevent, and address security issues"}),(0,d.jsx)("li",{children:"Comply with legal obligations"}),(0,d.jsx)("li",{children:"Serve relevant advertisements through third-party services"})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"4. Advertising and Third-Party Services"}),(0,d.jsx)("h3",{className:"text-xl font-medium mb-3",children:"4.1 Google AdSense"}),(0,d.jsx)("p",{children:"We use Google AdSense to display advertisements on our website. Google AdSense uses cookies and similar technologies to serve ads based on your prior visits to our website or other websites. These ads may be based on your interests and demographics."}),(0,d.jsx)("h3",{className:"text-xl font-medium mb-3 mt-6",children:"4.2 Advertising Cookies"}),(0,d.jsx)("p",{children:"Third-party vendors, including Google, use cookies to serve ads based on your prior visits to our website. You may opt out of personalized advertising by visiting Google's Ads Settings or the Network Advertising Initiative opt-out page."}),(0,d.jsx)("h3",{className:"text-xl font-medium mb-3 mt-6",children:"4.3 Third-Party Analytics"}),(0,d.jsx)("p",{children:"We use third-party analytics services including:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Google Analytics:"})," Tracks website usage and performance"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Google Tag Manager:"})," Manages tracking codes and scripts"]})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"5. Cookies and Tracking Technologies"}),(0,d.jsx)("p",{children:"We use cookies and similar tracking technologies to:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsx)("li",{children:"Remember your preferences and settings"}),(0,d.jsx)("li",{children:"Analyze website traffic and usage patterns"}),(0,d.jsx)("li",{children:"Provide personalized content and advertisements"}),(0,d.jsx)("li",{children:"Improve website security and functionality"})]}),(0,d.jsx)("p",{className:"mt-4",children:"You can control cookies through your browser settings. However, disabling cookies may affect website functionality."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"6. Information Sharing and Disclosure"}),(0,d.jsx)("p",{children:"We may share your information in the following circumstances:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"With your consent:"})," When you explicitly agree to sharing"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Service providers:"})," With third-party vendors who assist in operating our services"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Legal requirements:"})," When required by law or to protect our rights"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Business transfers:"})," In connection with mergers, acquisitions, or asset sales"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Public content:"})," Content you choose to make public (shared rules, profiles)"]})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"7. Data Security"}),(0,d.jsx)("p",{children:"We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"8. Data Retention"}),(0,d.jsx)("p",{children:"We retain your personal information for as long as necessary to provide our services, comply with legal obligations, resolve disputes, and enforce our agreements. Account information is retained until you delete your account."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"9. Your Privacy Rights"}),(0,d.jsx)("p",{children:"Depending on your location, you may have the following rights:"}),(0,d.jsxs)("ul",{className:"list-disc pl-6 mt-2",children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Access:"})," Request access to your personal information"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Correction:"})," Request correction of inaccurate information"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Deletion:"})," Request deletion of your personal information"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Portability:"})," Request transfer of your data"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Opt-out:"})," Opt out of certain data processing activities"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Withdraw consent:"})," Withdraw consent for data processing"]})]})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"10. International Data Transfers"}),(0,d.jsx)("p",{children:"Your information may be transferred to and processed in countries other than your country of residence. We ensure appropriate safeguards are in place to protect your personal information during such transfers."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"11. Children's Privacy"}),(0,d.jsx)("p",{children:"Our services are not directed to children under 13 years of age. We do not knowingly collect personal information from children under 13. If we discover that we have collected information from a child under 13, we will delete it immediately."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"12. California Privacy Rights (CCPA)"}),(0,d.jsx)("p",{children:"California residents have additional privacy rights under the California Consumer Privacy Act (CCPA), including the right to know about personal information collected, sold, or disclosed, and the right to request deletion of personal information."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"13. European Privacy Rights (GDPR)"}),(0,d.jsx)("p",{children:"If you are in the European Economic Area (EEA), you have rights under the General Data Protection Regulation (GDPR), including the right to access, rectify, erase, restrict processing, object to processing, and data portability."})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"14. Changes to This Privacy Policy"}),(0,d.jsx)("p",{children:'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date. We encourage you to review this Privacy Policy periodically.'})]}),(0,d.jsxs)("section",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"15. Contact Us"}),(0,d.jsx)("p",{children:"If you have any questions about this Privacy Policy or our privacy practices, please contact us through our website or support channels."}),(0,d.jsxs)("div",{className:"mt-6 p-4 bg-muted rounded-lg",children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Opt-Out Links:"}),(0,d.jsxs)("ul",{className:"space-y-1 text-sm",children:[(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"https://www.google.com/settings/ads",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Google Ads Settings"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"https://optout.networkadvertising.org/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Network Advertising Initiative Opt-Out"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"https://optout.aboutads.info/",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Digital Advertising Alliance Opt-Out"})})]})]})]})]})})}},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91491:(a,b,c)=>{c.r(b),c.d(b,{default:()=>g,dynamic:()=>e,metadata:()=>f});var d=c(38828);c(21971);let e="force-dynamic",f={title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.",keywords:"AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy",authors:[{name:"OnlyRules Team"}],openGraph:{title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs.",type:"website"}};function g({children:a}){return(0,d.jsx)("div",{className:"dark",children:(0,d.jsx)("div",{className:"min-h-screen bg-background flex flex-col",children:a})})}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,5208,3512,4544,709],()=>b(b.s=28140));module.exports=c})();