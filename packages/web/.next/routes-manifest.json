{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/404", "destination": "/not-found", "statusCode": 307, "regex": "^(?!/_next)/404(?:/)?$"}, {"source": "/500", "destination": "/error", "statusCode": 307, "regex": "^(?!/_next)/500(?:/)?$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Cache-Control", "value": "no-store, must-revalidate"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/sitemap.xml", "destination": "/api/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$"}, {"source": "/robots.txt", "destination": "/api/robots.txt", "regex": "^/robots\\.txt(?:/)?$"}], "fallback": []}, "dynamicRoutes": [{"page": "/api/auth/[...all]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPall": "nxtPall"}, "namedRegex": "^/api/auth/(?<nxtPall>.+?)(?:/)?$"}, {"page": "/api/rules/[id]", "regex": "^/api/rules/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/rules/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/rulesets/[id]", "regex": "^/api/rulesets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/rulesets/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/docs/[[...slug]]", "regex": "^/docs(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/docs(?:/(?<nxtPslug>.+?))?(?:/)?$"}, {"page": "/profile/[username]", "regex": "^/profile/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername"}, "namedRegex": "^/profile/(?<nxtPusername>[^/]+?)(?:/)?$"}, {"page": "/r/[id]", "regex": "^/r/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/r/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/rules/[id]", "regex": "^/rules/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/rules/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/rulesets/[id]", "regex": "^/rulesets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/rulesets/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/ides", "regex": "^/ides(?:/)?$", "routeKeys": {}, "namedRegex": "^/ides(?:/)?$"}, {"page": "/ides/augment", "regex": "^/ides/augment(?:/)?$", "routeKeys": {}, "namedRegex": "^/ides/augment(?:/)?$"}, {"page": "/ides/claude", "regex": "^/ides/claude(?:/)?$", "routeKeys": {}, "namedRegex": "^/ides/claude(?:/)?$"}, {"page": "/ides/cline", "regex": "^/ides/cline(?:/)?$", "routeKeys": {}, "namedRegex": "^/ides/cline(?:/)?$"}, {"page": "/ides/cursor", "regex": "^/ides/cursor(?:/)?$", "routeKeys": {}, "namedRegex": "^/ides/cursor(?:/)?$"}, {"page": "/ides/github-copilot", "regex": "^/ides/github\\-copilot(?:/)?$", "routeKeys": {}, "namedRegex": "^/ides/github\\-copilot(?:/)?$"}, {"page": "/ides/windsurf", "regex": "^/ides/windsurf(?:/)?$", "routeKeys": {}, "namedRegex": "^/ides/windsurf(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/rulesets", "regex": "^/rulesets(?:/)?$", "routeKeys": {}, "namedRegex": "^/rulesets(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/templates", "regex": "^/templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/templates(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/test-theme", "regex": "^/test\\-theme(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-theme(?:/)?$"}, {"page": "/tutorials", "regex": "^/tutorials(?:/)?$", "routeKeys": {}, "namedRegex": "^/tutorials(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}