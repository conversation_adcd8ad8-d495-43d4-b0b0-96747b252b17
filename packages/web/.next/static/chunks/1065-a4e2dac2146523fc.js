(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1065],{3997:(e,t,r)=>{"use strict";r.d(t,{S:()=>b});var s=r(7620),n=r(77785),a=r(79649),i=r(74531),l=r(33233),o=r(63135),u=r(88796);let p={...i.f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","solid","soft","surface","outline","ghost"],default:"solid"},...l.un,...o.Z,...u.F,loading:{type:"boolean",className:"rt-loading",default:!1}};var c=r(64722);let m={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},loading:{type:"boolean",default:!0}};var d=r(55772),f=r(45225);let v=s.forwardRef((e,t)=>{let{className:r,children:a,loading:i,...l}=(0,d.o)(e,m,f.y);if(!i)return a;let o=s.createElement("span",{...l,ref:t,className:n("rt-Spinner",r)},s.createElement("span",{className:"rt-SpinnerLeaf"}),s.createElement("span",{className:"rt-SpinnerLeaf"}),s.createElement("span",{className:"rt-SpinnerLeaf"}),s.createElement("span",{className:"rt-SpinnerLeaf"}),s.createElement("span",{className:"rt-SpinnerLeaf"}),s.createElement("span",{className:"rt-SpinnerLeaf"}),s.createElement("span",{className:"rt-SpinnerLeaf"}),s.createElement("span",{className:"rt-SpinnerLeaf"}));return void 0===a?o:s.createElement(c.s,{asChild:!0,position:"relative",align:"center",justify:"center"},s.createElement("span",null,s.createElement("span",{"aria-hidden":!0,style:{display:"contents",visibility:"hidden"},inert:void 0},a),s.createElement(c.s,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},s.createElement("span",null,o))))});v.displayName="Spinner";var y=r(81603);let g=y.bL;y.bL;var N=r(26819);let b=s.forwardRef((e,t)=>{let{size:r=p.size.default}=e,{className:i,children:l,asChild:o,color:u,radius:m,disabled:y=e.loading,...b}=(0,d.o)(e,p,f.y),h=o?a.bL:"button";return s.createElement(h,{"data-disabled":y||void 0,"data-accent-color":u,"data-radius":m,...b,ref:t,className:n("rt-reset","rt-BaseButton",i),disabled:y},e.loading?s.createElement(s.Fragment,null,s.createElement("span",{style:{display:"contents",visibility:"hidden"},"aria-hidden":!0},l),s.createElement(g,null,l),s.createElement(c.s,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},s.createElement("span",null,s.createElement(v,{size:(0,N.AY)(r,N.fW)})))):l)});b.displayName="BaseButton"},7156:(e,t,r)=>{"use strict";r.d(t,{bL:()=>u,hO:()=>o,sG:()=>l});var s=r(7620),n=r(97509),a=r(79649),i=r(54568),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),n=s.forwardRef((e,s)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...a,ref:s})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function o(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}var u=l},8903:(e,t,r)=>{"use strict";function s(...e){let t={};for(let r of e)r&&(t={...t,...r});return Object.keys(t).length?t:void 0}r.d(t,{Z:()=>s})},11448:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});let s=["0","1","2","3","4","5","6","7","8","9"],n={p:{type:"enum | string",className:"rt-r-p",customProperties:["--p"],values:s,responsive:!0},px:{type:"enum | string",className:"rt-r-px",customProperties:["--pl","--pr"],values:s,responsive:!0},py:{type:"enum | string",className:"rt-r-py",customProperties:["--pt","--pb"],values:s,responsive:!0},pt:{type:"enum | string",className:"rt-r-pt",customProperties:["--pt"],values:s,responsive:!0},pr:{type:"enum | string",className:"rt-r-pr",customProperties:["--pr"],values:s,responsive:!0},pb:{type:"enum | string",className:"rt-r-pb",customProperties:["--pb"],values:s,responsive:!0},pl:{type:"enum | string",className:"rt-r-pl",customProperties:["--pl"],values:s,responsive:!0}}},13096:(e,t,r)=>{"use strict";r.d(t,{f:()=>s});let s=["initial","xs","sm","md","lg","xl"]},26819:(e,t,r)=>{"use strict";function s(e,t){if(void 0!==e)return"string"==typeof e?t(e):Object.fromEntries(Object.entries(e).map(([e,r])=>[e,t(r)]))}function n(e){return"3"===e?"3":"2"}function a(e){switch(e){case"1":return"1";case"2":case"3":return"2";case"4":return"3"}}r.d(t,{AY:()=>s,Rw:()=>n,fW:()=>a})},33233:(e,t,r)=>{"use strict";r.d(t,{Ag:()=>n,XA:()=>s,_s:()=>a,un:()=>i});let s=["gray","gold","bronze","brown","yellow","amber","orange","tomato","red","ruby","crimson","pink","plum","purple","violet","iris","indigo","blue","cyan","teal","jade","green","grass","lime","mint","sky"],n=["auto","gray","mauve","slate","sage","olive","sand"],a={color:{type:"enum",values:s,default:void 0}},i={color:{type:"enum",values:s,default:""}}},38821:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});let s=["0","1","2","3","4","5","6","7","8","9"],n={gap:{type:"enum | string",className:"rt-r-gap",customProperties:["--gap"],values:s,responsive:!0},gapX:{type:"enum | string",className:"rt-r-cg",customProperties:["--column-gap"],values:s,responsive:!0},gapY:{type:"enum | string",className:"rt-r-rg",customProperties:["--row-gap"],values:s,responsive:!0}}},41084:(e,t,r)=>{"use strict";r.d(t,{B:()=>s});let s={height:{type:"string",className:"rt-r-h",customProperties:["--height"],responsive:!0},minHeight:{type:"string",className:"rt-r-min-h",customProperties:["--min-height"],responsive:!0},maxHeight:{type:"string",className:"rt-r-max-h",customProperties:["--max-height"],responsive:!0}}},43637:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var s=r(11448),n=r(41084),a=r(68405);let i=["visible","hidden","clip","scroll","auto"],l=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],o={...s.T,...a.w,...n.B,position:{type:"enum",className:"rt-r-position",values:["static","relative","absolute","fixed","sticky"],responsive:!0},inset:{type:"enum | string",className:"rt-r-inset",customProperties:["--inset"],values:l,responsive:!0},top:{type:"enum | string",className:"rt-r-top",customProperties:["--top"],values:l,responsive:!0},right:{type:"enum | string",className:"rt-r-right",customProperties:["--right"],values:l,responsive:!0},bottom:{type:"enum | string",className:"rt-r-bottom",customProperties:["--bottom"],values:l,responsive:!0},left:{type:"enum | string",className:"rt-r-left",customProperties:["--left"],values:l,responsive:!0},overflow:{type:"enum",className:"rt-r-overflow",values:i,responsive:!0},overflowX:{type:"enum",className:"rt-r-ox",values:i,responsive:!0},overflowY:{type:"enum",className:"rt-r-oy",values:i,responsive:!0},flexBasis:{type:"string",className:"rt-r-fb",customProperties:["--flex-basis"],responsive:!0},flexShrink:{type:"enum | string",className:"rt-r-fs",customProperties:["--flex-shrink"],values:["0","1"],responsive:!0},flexGrow:{type:"enum | string",className:"rt-r-fg",customProperties:["--flex-grow"],values:["0","1"],responsive:!0},gridArea:{type:"string",className:"rt-r-ga",customProperties:["--grid-area"],responsive:!0},gridColumn:{type:"string",className:"rt-r-gc",customProperties:["--grid-column"],responsive:!0},gridColumnStart:{type:"string",className:"rt-r-gcs",customProperties:["--grid-column-start"],responsive:!0},gridColumnEnd:{type:"string",className:"rt-r-gce",customProperties:["--grid-column-end"],responsive:!0},gridRow:{type:"string",className:"rt-r-gr",customProperties:["--grid-row"],responsive:!0},gridRowStart:{type:"string",className:"rt-r-grs",customProperties:["--grid-row-start"],responsive:!0},gridRowEnd:{type:"string",className:"rt-r-gre",customProperties:["--grid-row-end"],responsive:!0}}},45225:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});let s=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],n={m:{type:"enum | string",values:s,responsive:!0,className:"rt-r-m",customProperties:["--m"]},mx:{type:"enum | string",values:s,responsive:!0,className:"rt-r-mx",customProperties:["--ml","--mr"]},my:{type:"enum | string",values:s,responsive:!0,className:"rt-r-my",customProperties:["--mt","--mb"]},mt:{type:"enum | string",values:s,responsive:!0,className:"rt-r-mt",customProperties:["--mt"]},mr:{type:"enum | string",values:s,responsive:!0,className:"rt-r-mr",customProperties:["--mr"]},mb:{type:"enum | string",values:s,responsive:!0,className:"rt-r-mb",customProperties:["--mb"]},ml:{type:"enum | string",values:s,responsive:!0,className:"rt-r-ml",customProperties:["--ml"]}}},49640:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>a});var s=r(7620);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return s.useCallback(a(...e),e)}},54594:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(74531),n=r(38821);let a={as:{type:"enum",values:["div","span"],default:"div"},...s.f,display:{type:"enum",className:"rt-r-display",values:["none","inline-flex","flex"],responsive:!0},direction:{type:"enum",className:"rt-r-fd",values:["row","column","row-reverse","column-reverse"],responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end","between"],parseValue:function(e){return"between"===e?"space-between":e},responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},...n.o}},55772:(e,t,r)=>{"use strict";r.d(t,{o:()=>l});var s=r(77785),n=r(61831),a=r(74469),i=r(8903);function l(e,...t){let r,o,u={...e},p=function(...e){return Object.assign({},...e)}(...t);for(let e in p){let t=u[e],l=p[e];if(void 0!==l.default&&void 0===t&&(t=l.default),"enum"!==l.type||[l.default,...l.values].includes(t)||(0,a.O)(t)||(t=l.default),u[e]=t,"className"in l&&l.className){delete u[e];let p="responsive"in l;if(!t||(0,a.O)(t)&&!p)continue;if((0,a.O)(t)&&(void 0!==l.default&&void 0===t.initial&&(t.initial=l.default),"enum"===l.type&&([l.default,...l.values].includes(t.initial)||(t.initial=l.default))),"enum"===l.type){r=s(r,(0,n.J_)({allowArbitraryValues:!1,value:t,className:l.className,propValues:l.values,parseValue:l.parseValue}));continue}if("string"===l.type||"enum | string"===l.type){let e="string"===l.type?[]:l.values,[a,u]=(0,n.tF)({className:l.className,customProperties:l.customProperties,propValues:e,parseValue:l.parseValue,value:t});o=(0,i.Z)(o,u),r=s(r,a);continue}if("boolean"===l.type&&t){r=s(r,l.className);continue}}}return u.className=s(r,e.className),u.style=(0,i.Z)(o,e.style),u}},61065:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var s=r(7620),n=r(77785),a=r(3997);let i=s.forwardRef(({className:e,...t},r)=>s.createElement(a.S,{...t,ref:r,className:n("rt-Button",e)}));i.displayName="Button"},61831:(e,t,r)=>{"use strict";r.d(t,{J_:()=>l,tF:()=>i});var s=r(13096);function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var a=r(74469);function i({className:e,customProperties:t,...r}){return[l({allowArbitraryValues:!0,className:e,...r}),function({customProperties:e,value:t,propValues:r,parseValue:i=e=>e}){let l={};if(!(!t||"string"==typeof t&&r.includes(t))){if("string"==typeof t&&(l=Object.fromEntries(e.map(e=>[e,t]))),(0,a.O)(t))for(let a in t){if(!n(t,a)||!s.f.includes(a))continue;let i=t[a];if(!r.includes(i))for(let t of e)l={["initial"===a?t:`${t}-${a}`]:i,...l}}for(let e in l){let t=l[e];void 0!==t&&(l[e]=i(t))}return l}}({customProperties:t,...r})]}function l({allowArbitraryValues:e,value:t,className:r,propValues:i,parseValue:l=e=>e}){let u=[];if(t){if("string"==typeof t&&i.includes(t))return o(r,t,l);if((0,a.O)(t)){for(let a in t){if(!n(t,a)||!s.f.includes(a))continue;let p=t[a];if(void 0!==p){if(i.includes(p)){let e=o(r,p,l),t="initial"===a?e:`${a}:${e}`;u.push(t)}else if(e){let e="initial"===a?r:`${a}:${r}`;u.push(e)}}}return u.join(" ")}if(e)return r}}function o(e,t,r){let s=r(t),n=s?.startsWith("-"),a=n?s?.substring(1):s;return`${n?"-":""}${e}${e?"-":""}${a}`}},62069:(e,t,r)=>{"use strict";r.d(t,{DX:()=>n});var s=r(79649);s.bL;let n=s.bL;s.xV},63135:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s={highContrast:{type:"boolean",className:"rt-high-contrast",default:void 0}}},64722:(e,t,r)=>{"use strict";r.d(t,{s:()=>p});var s=r(7620),n=r(77785),a=r(55772),i=r(43637),l=r(45225),o=r(62069),u=r(54594);let p=s.forwardRef((e,t)=>{let{className:r,asChild:p,as:c="div",...m}=(0,a.o)(e,u.F,i.i,l.y);return s.createElement(p?o.DX:c,{...m,ref:t,className:n("rt-Flex",r)})});p.displayName="Flex"},68405:(e,t,r)=>{"use strict";r.d(t,{w:()=>s});let s={width:{type:"string",className:"rt-r-w",customProperties:["--width"],responsive:!0},minWidth:{type:"string",className:"rt-r-min-w",customProperties:["--min-width"],responsive:!0},maxWidth:{type:"string",className:"rt-r-max-w",customProperties:["--max-width"],responsive:!0}}},74469:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});var s=r(13096);function n(e){return"object"==typeof e&&Object.keys(e).some(e=>s.f.includes(e))}},74531:(e,t,r)=>{"use strict";r.d(t,{f:()=>s});let s={asChild:{type:"boolean"}}},77785:(e,t)=>{var r;!function(){"use strict";var s={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=a(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)s.call(e,r)&&e[r]&&(t=a(t,r));return t}(r)))}return e}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):void 0===(r=(function(){return n}).apply(t,[]))||(e.exports=r)}()},79649:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>u,TL:()=>i,bL:()=>l,xV:()=>p});var s=r(7620),n=r(49640),a=r(54568);function i(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let r={...t};for(let s in t){let n=e[s],a=t[s];/^on[A-Z]/.test(s)?n&&a?r[s]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...a}:"className"===s&&(r[s]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==s.Fragment&&(u.ref=t?(0,n.t)(t,o):o),s.cloneElement(r,u)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...i}=e,l=s.Children.toArray(n),o=l.find(c);if(o){let e=o.props.children,n=l.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}var p=u("Slottable");function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},81603:(e,t,r)=>{"use strict";r.d(t,{Qg:()=>i,bL:()=>o,s6:()=>l});var s=r(7620),n=r(7156),a=r(54568),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=s.forwardRef((e,t)=>(0,a.jsx)(n.sG.span,{...e,ref:t,style:{...i,...e.style}}));l.displayName="VisuallyHidden";var o=l},88796:(e,t,r)=>{"use strict";r.d(t,{F:()=>n,O:()=>s});let s=["none","small","medium","large","full"],n={radius:{type:"enum",values:s,default:void 0}}}}]);