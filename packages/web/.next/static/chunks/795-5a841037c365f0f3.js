"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[795],{5007:(e,t,r)=>{r.d(t,{b:()=>n});let n={align:{type:"enum",className:"rt-r-ta",values:["left","center","right"],responsive:!0}}},5577:(e,t,r)=>{r.d(t,{RG:()=>R,bL:()=>M,q7:()=>I});var n=r(7620),l=r(29254),i=r(91675),o=r(49640),s=r(80482),u=r(60728),a=r(7156),c=r(93568),f=r(87076),d=r(17644),p=r(54568),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[y,w,g]=(0,i.N)(v),[N,R]=(0,s.A)(v,[g]),[b,x]=N(v),A=n.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(E,{...e,ref:t})})}));A.displayName=v;var E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:u,currentTabStopId:y,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:N,onEntryFocus:R,preventScrollOnEntryFocus:x=!1,...A}=e,E=n.useRef(null),O=(0,o.s)(t,E),S=(0,d.jH)(u),[T,M]=(0,f.i)({prop:y,defaultProp:null!=g?g:null,onChange:N,caller:v}),[I,j]=n.useState(!1),P=(0,c.c)(R),k=w(r),L=n.useRef(!1),[_,D]=n.useState(0);return n.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(h,P),()=>e.removeEventListener(h,P)},[P]),(0,p.jsx)(b,{scope:r,orientation:i,dir:S,loop:s,currentTabStopId:T,onItemFocus:n.useCallback(e=>M(e),[M]),onItemShiftTab:n.useCallback(()=>j(!0),[]),onFocusableItemAdd:n.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>D(e=>e-1),[]),children:(0,p.jsx)(a.sG.div,{tabIndex:I||0===_?-1:0,"data-orientation":i,...A,ref:O,style:{outline:"none",...e.style},onMouseDown:(0,l.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,l.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!I){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);C([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),x)}}L.current=!1}),onBlur:(0,l.m)(e.onBlur,()=>j(!1))})})}),O="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:o=!1,tabStopId:s,children:c,...f}=e,d=(0,u.B)(),h=s||d,m=x(O,r),v=m.currentTabStopId===h,g=w(r),{onFocusableItemAdd:N,onFocusableItemRemove:R,currentTabStopId:b}=m;return n.useEffect(()=>{if(i)return N(),()=>R()},[i,N,R]),(0,p.jsx)(y.ItemSlot,{scope:r,id:h,focusable:i,active:o,children:(0,p.jsx)(a.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{i?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,l.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,l.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let l=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(l))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(l)))return T[l]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>C(r))}}),children:"function"==typeof c?c({isCurrentTabStop:v,hasTabStop:null!=b}):c})})});S.displayName=O;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function C(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var M=A,I=S},12849:(e,t,r)=>{r.d(t,{$:()=>n});let n={trim:{type:"enum",className:"rt-r-lt",values:["normal","start","end","both"],responsive:!0}}},13936:(e,t,r)=>{r.d(t,{J:()=>n});let n={truncate:{type:"boolean",className:"rt-truncate"}}},17247:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(7620),l=globalThis?.document?n.useLayoutEffect:()=>{}},17644:(e,t,r)=>{r.d(t,{FX:()=>o,Kq:()=>u,jH:()=>s});var n=r(7620),l=r(54568),i=n.createContext(void 0),o=e=>{let{dir:t,children:r}=e;return(0,l.jsx)(i.Provider,{value:t,children:r})};function s(e){let t=n.useContext(i);return e||t||"ltr"}var u=o},18400:(e,t,r)=>{r.d(t,{C:()=>o});var n=r(7620),l=r(49640),i=r(17247),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[l,o]=n.useState(),u=n.useRef(null),a=n.useRef(e),c=n.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=s(u.current);c.current="mounted"===f?e:"none"},[f]),(0,i.N)(()=>{let t=u.current,r=a.current;if(r!==e){let n=c.current,l=s(t);e?d("MOUNT"):"none"===l||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==l?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),(0,i.N)(()=>{if(l){var e;let t,r=null!=(e=l.ownerDocument.defaultView)?e:window,n=e=>{let n=s(u.current).includes(e.animationName);if(e.target===l&&n&&(d("ANIMATION_END"),!a.current)){let e=l.style.animationFillMode;l.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=e)})}},i=e=>{e.target===l&&(c.current=s(u.current))};return l.addEventListener("animationstart",i),l.addEventListener("animationcancel",n),l.addEventListener("animationend",n),()=>{r.clearTimeout(t),l.removeEventListener("animationstart",i),l.removeEventListener("animationcancel",n),l.removeEventListener("animationend",n)}}d("ANIMATION_END")},[l,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(e=>{u.current=e?getComputedStyle(e):null,o(e)},[])}}(t),u="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=(0,l.s)(o.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,l=n&&"isReactWarning"in n&&n.isReactWarning;return l?e.ref:(l=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof r||o.isPresent?n.cloneElement(u,{ref:a}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},29254:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},45798:(e,t,r)=>{r.d(t,{L:()=>n});let n={weight:{type:"enum",className:"rt-r-weight",values:["light","regular","medium","bold"],responsive:!0}}},51278:(e,t,r)=>{r.d(t,{G:()=>n});let n={wrap:{type:"enum",className:"rt-r-tw",values:["wrap","nowrap","pretty","balance"],responsive:!0}}},60728:(e,t,r)=>{r.d(t,{B:()=>u});var n,l=r(7620),i=r(17247),o=(n||(n=r.t(l,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function u(e){let[t,r]=l.useState(o());return(0,i.N)(()=>{e||r(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},68082:(e,t,r)=>{r.d(t,{E:()=>y});var n=r(7620),l=r(77785),i=r(79649),o=r(55772),s=r(45225),u=r(74531),a=r(33233),c=r(63135),f=r(12849),d=r(5007),p=r(51278),h=r(13936),m=r(45798);let v={as:{type:"enum",values:["span","div","label","p"],default:"span"},...u.f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],responsive:!0},...m.L,...d.b,...f.$,...h.J,...p.G,...a._s,...c.Z},y=n.forwardRef((e,t)=>{let{children:r,className:u,asChild:a,as:c="span",color:f,...d}=(0,o.o)(e,v,s.y);return n.createElement(i.bL,{"data-accent-color":f,...d,ref:t,className:l("rt-Text",u)},a?r:n.createElement(c,null,r))});y.displayName="Text"},80482:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>i});var n=r(7620),l=r(54568);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,o=n.useMemo(()=>i,Object.values(i));return(0,l.jsx)(r.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(l){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return i.scopeName=e,[function(t,i){let o=n.createContext(i),s=r.length;r=[...r,i];let u=t=>{let{scope:r,children:i,...u}=t,a=r?.[e]?.[s]||o,c=n.useMemo(()=>u,Object.values(u));return(0,l.jsx)(a.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(r,l){let u=l?.[e]?.[s]||o,a=n.useContext(u);if(a)return a;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(i,...t)]}},87076:(e,t,r)=>{r.d(t,{i:()=>s});var n,l=r(7620),i=r(17247),o=(n||(n=r.t(l,2)))[" useInsertionEffect ".trim().toString()]||i.N;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,s,u]=function({defaultProp:e,onChange:t}){let[r,n]=l.useState(e),i=l.useRef(r),s=l.useRef(t);return o(()=>{s.current=t},[t]),l.useEffect(()=>{i.current!==r&&(s.current?.(r),i.current=r)},[r,i]),[r,n,s]}({defaultProp:t,onChange:r}),a=void 0!==e,c=a?e:i;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,n])}return[c,l.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u.current?.(r)}else s(t)},[a,e,s,u])]}Symbol("RADIX:SYNC_STATE")},91675:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function l(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function i(e,t,r){var l=n(e,t,"set");if(l.set)l.set.call(e,r);else{if(!l.writable)throw TypeError("attempted to set read only private field");l.value=r}return r}r.d(t,{N:()=>d,C:()=>y});var o,s=r(7620),u=r(80482),a=r(49640),c=r(79649),f=r(54568);function d(e){let t=e+"CollectionProvider",[r,n]=(0,u.A)(t),[l,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:r}=e,n=s.useRef(null),i=s.useRef(new Map).current;return(0,f.jsx)(l,{scope:t,itemMap:i,collectionRef:n,children:r})};o.displayName=t;let d=e+"CollectionSlot",p=(0,c.TL)(d),h=s.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=i(d,r),o=(0,a.s)(t,l.collectionRef);return(0,f.jsx)(p,{ref:o,children:n})});h.displayName=d;let m=e+"CollectionItemSlot",v="data-radix-collection-item",y=(0,c.TL)(m),w=s.forwardRef((e,t)=>{let{scope:r,children:n,...l}=e,o=s.useRef(null),u=(0,a.s)(t,o),c=i(m,r);return s.useEffect(()=>(c.itemMap.set(o,{ref:o,...l}),()=>void c.itemMap.delete(o))),(0,f.jsx)(y,{...{[v]:""},ref:u,children:n})});return w.displayName=m,[{Provider:o,Slot:h,ItemSlot:w},function(t){let r=i(e+"CollectionConsumer",t);return s.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var p=new WeakMap,h=(o=new WeakMap,class e extends Map{set(e,t){return p.get(this)&&(this.has(e)?l(this,o)[l(this,o).indexOf(e)]=e:l(this,o).push(e)),super.set(e,t),this}insert(e,t,r){let n,i=this.has(t),s=l(this,o).length,u=v(e),a=u>=0?u:s+u,c=a<0||a>=s?-1:a;if(c===this.size||i&&c===this.size-1||-1===c)return this.set(t,r),this;let f=this.size+ +!i;u<0&&a++;let d=[...l(this,o)],p=!1;for(let e=a;e<f;e++)if(a===e){let l=d[e];d[e]===t&&(l=d[e+1]),i&&this.delete(t),n=this.get(l),this.set(t,r)}else{p||d[e-1]!==t||(p=!0);let r=d[p?e:e-1],l=n;n=this.get(r),this.delete(r),this.set(r,l)}return this}with(t,r,n){let l=new e(this);return l.insert(t,r,n),l}before(e){let t=l(this,o).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,r){let n=l(this,o).indexOf(e);return -1===n?this:this.insert(n,t,r)}after(e){let t=l(this,o).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,r){let n=l(this,o).indexOf(e);return -1===n?this:this.insert(n+1,t,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return i(this,o,[]),super.clear()}delete(e){let t=super.delete(e);return t&&l(this,o).splice(l(this,o).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=m(l(this,o),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=m(l(this,o),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return l(this,o).indexOf(e)}keyAt(e){return m(l(this,o),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.at(n)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.keyAt(n)}find(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return n;r++}}findIndex(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return r;r++}return -1}filter(t,r){let n=[],l=0;for(let e of this)Reflect.apply(t,r,[e,l,this])&&n.push(e),l++;return new e(n)}map(t,r){let n=[],l=0;for(let e of this)n.push([e[0],Reflect.apply(t,r,[e,l,this])]),l++;return new e(n)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,l]=t,i=0,o=null!=l?l:this.at(0);for(let e of this)o=0===i&&1===t.length?e:Reflect.apply(n,this,[o,e,i,this]),i++;return o}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,l]=t,i=null!=l?l:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);i=e===this.size-1&&1===t.length?r:Reflect.apply(n,this,[i,r,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),n=this.get(r);t.set(r,n)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let l=[...this.entries()];return l.splice(...r),new e(l)}slice(t,r){let n=new e,l=this.size-1;if(void 0===t)return n;t<0&&(t+=this.size),void 0!==r&&r>0&&(l=r-1);for(let e=t;e<=l;e++){let t=this.keyAt(e),r=this.get(t);n.set(t,r)}return n}every(e,t){let r=0;for(let n of this){if(!Reflect.apply(e,t,[n,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,o,{writable:!0,value:void 0}),i(this,o,[...super.keys()]),p.set(this,!0)}});function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=v(t),l=n>=0?n:r+n;return l<0||l>=r?-1:l}(e,t);return -1===r?void 0:e[r]}function v(e){return e!=e||0===e?0:Math.trunc(e)}function y(e){let t=e+"CollectionProvider",[r,n]=(0,u.A)(t),[l,i]=r(t,{collectionElement:null,collectionRef:{current:null},collectionRefObject:{current:null},itemMap:new h,setItemMap:()=>void 0}),o=e=>{let{state:t,...r}=e;return t?(0,f.jsx)(p,{...r,state:t}):(0,f.jsx)(d,{...r})};o.displayName=t;let d=e=>{let t=b();return(0,f.jsx)(p,{...e,state:t})};d.displayName=t+"Init";let p=e=>{let{scope:t,children:r,state:n}=e,i=s.useRef(null),[o,u]=s.useState(null),c=(0,a.s)(i,u),[d,p]=n;return s.useEffect(()=>{var e;if(!o)return;let t=(e=()=>{},new MutationObserver(t=>{for(let r of t)if("childList"===r.type)return void e()}));return t.observe(o,{childList:!0,subtree:!0}),()=>{t.disconnect()}},[o]),(0,f.jsx)(l,{scope:t,itemMap:d,setItemMap:p,collectionRef:c,collectionRefObject:i,collectionElement:o,children:r})};p.displayName=t+"Impl";let m=e+"CollectionSlot",v=(0,c.TL)(m),y=s.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=i(m,r),o=(0,a.s)(t,l.collectionRef);return(0,f.jsx)(v,{ref:o,children:n})});y.displayName=m;let g=e+"CollectionItemSlot",N=(0,c.TL)(g),R=s.forwardRef((e,t)=>{let{scope:r,children:n,...l}=e,o=s.useRef(null),[u,c]=s.useState(null),d=(0,a.s)(t,o,c),{setItemMap:p}=i(g,r),m=s.useRef(l);!function(e,t){if(e===t)return!0;if("object"!=typeof e||"object"!=typeof t||null==e||null==t)return!1;let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let n of r)if(!Object.prototype.hasOwnProperty.call(t,n)||e[n]!==t[n])return!1;return!0}(m.current,l)&&(m.current=l);let v=m.current;return s.useEffect(()=>(p(e=>u?e.has(u)?e.set(u,{...v,element:u}).toSorted(w):(e.set(u,{...v,element:u}),e.toSorted(w)):e),()=>{p(e=>u&&e.has(u)?(e.delete(u),new h(e)):e)}),[u,v,p]),(0,f.jsx)(N,{"data-radix-collection-item":"",ref:d,children:n})});function b(){return s.useState(new h)}return R.displayName=g,[{Provider:o,Slot:y,ItemSlot:R},{createCollectionScope:n,useCollection:function(t){let{itemMap:r}=i(e+"CollectionConsumer",t);return r},useInitCollection:b}]}function w(e,t){var r;return e[1].element&&t[1].element?(r=e[1].element,t[1].element.compareDocumentPosition(r)&Node.DOCUMENT_POSITION_PRECEDING)?-1:1:0}},93568:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(7620);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}}}]);