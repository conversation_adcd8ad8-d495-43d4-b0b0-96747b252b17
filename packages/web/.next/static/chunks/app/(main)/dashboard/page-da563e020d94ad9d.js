(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5660],{24237:(e,s,t)=>{Promise.resolve().then(t.bind(t,91718))},24772:(e,s,t)=>{"use strict";t.d(s,{l:()=>n,q:()=>a});var l=t(78021),i=t(51874);let a=new l.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,s)=>(!((null==s?void 0:s.status)>=400)||!((null==s?void 0:s.status)<500))&&e<3,refetchOnWindowFocus:!1,throwOnError:!1},mutations:{retry:!1,onError:e=>{console.error("Mutation error:",e);let s=(null==e?void 0:e.message)||"An error occurred";i.oR.error(s)}}}}),n={rules:{all:["rules"],lists:()=>[...n.rules.all,"list"],list:e=>[...n.rules.lists(),e],details:()=>[...n.rules.all,"detail"],detail:e=>[...n.rules.details(),e],raw:e=>[...n.rules.all,"raw",e]},rulesets:{all:["rulesets"],lists:()=>[...n.rulesets.all,"list"],list:e=>[...n.rulesets.lists(),e],details:()=>[...n.rulesets.all,"detail"],detail:e=>[...n.rulesets.details(),e]},tags:{all:["tags"],lists:()=>[...n.tags.all,"list"]},user:{all:["user"],profile:e=>[...n.user.all,"profile",e]}}},58124:(e,s,t)=>{"use strict";t.d(s,{El:()=>d,fs:()=>o,qT:()=>c});var l=t(73297),i=t(87606),a=t(24772);t(51874);let n=async e=>{let s=await fetch("/api/rules/".concat(e));if(!s.ok)throw{message:404===s.status?"Rule not found":403===s.status?"You don't have permission to view this rule":"Failed to load rule",status:s.status};return s.json()},r=async function(){var e;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;s.search&&t.set("search",s.search),(null==(e=s.tags)?void 0:e.length)&&t.set("tags",s.tags.join(",")),s.ideType&&t.set("ideType",s.ideType),s.visibility&&t.set("visibility",s.visibility),s.page&&t.set("page",s.page.toString()),s.limit&&t.set("limit",s.limit.toString());let l=await fetch("/api/rules?".concat(t));if(!l.ok)throw Error("Failed to fetch rules");let i=await l.json();return{rules:Array.isArray(i)?i:i.rules||[],totalCount:i.totalCount||i.length||0,totalPages:i.totalPages||1,currentPage:i.currentPage||1}},c=e=>(0,l.I)({queryKey:a.l.rules.detail(e),queryFn:()=>n(e),enabled:!!e,staleTime:3e5,retry:(e,s)=>(null==s?void 0:s.status)!==404&&(null==s?void 0:s.status)!==403&&e<3}),o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,l.I)({queryKey:a.l.rules.list(e),queryFn:()=>r(e),staleTime:12e4,keepPreviousData:!0})},d=()=>{let e=(0,i.jE)();return()=>{e.invalidateQueries({queryKey:a.l.rules.all})}}},67642:(e,s,t)=>{"use strict";t.d(s,{CI:()=>a,Jv:()=>l,wV:()=>n});let{signIn:l,signUp:i,signOut:a,useSession:n,getSession:r}=(0,t(57664).MB)({baseURL:"https://onlyrules.codes"})},91718:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>el,dynamic:()=>et});var l=t(54568),i=t(7620),a=t(61065),n=t(83876),r=t(60057),c=t(68082),o=t(64722),d=t(77749),x=t(42716),h=t(58983),u=t(61689),m=t(19630),g=t(34964),p=t(64529),j=t(48976),v=t(82055),y=t(16350),f=t(85958),b=t(15173),N=t(44795),w=t(32461),C=t(51004),E=t(12463),I=t(24508),A=t(76945),R=t(19779),z=t(32987),S=t(60607);function T(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,S.QP)((0,z.$)(s))}let k=i.createContext({size:"2",variant:"soft"}),D=i.forwardRef((e,s)=>{let{className:t,variant:i,size:a,children:n,...r}=e;return(0,l.jsx)(R.Root,{ref:s,className:T("flex items-center justify-center gap-1",t),...r,children:(0,l.jsx)(k.Provider,{value:{variant:i,size:a},children:n})})});D.displayName=R.Root.displayName;let L=i.forwardRef((e,s)=>{let{className:t,children:i,variant:a,size:n,...r}=e;return(0,l.jsx)(R.Item,{ref:s,className:T("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",t),...r,children:i})});L.displayName=R.Item.displayName;var O=t(93425),F=t(69244),P=t(81357),U=t(26330),$=t(76430),B=t(74711),G=t(42879),_=t(76608),M=t(68262);function V(e){if(!e.trim())return[{id:K(),title:"Section 1",content:""}];let s=e.split(/^---\s*$/m);if(1===s.length){let s=e.match(/^#\s+(.+)/m),t=s?s[1].trim():"Section 1";return[{id:K(),title:t,content:e.trim()}]}let t=[],l={};for(let e=0;e<s.length;e++){let i=s[e].trim();if(!i)continue;let a=i.split("\n"),n=!0,r={};for(let e of a){let s=e.trim();if(!s)continue;let t=s.match(/^(description|name|globs):\s*(.+)$/);if(t){let[,e,s]=t;r[e]=s.trim()}else{n=!1;break}}if(n&&Object.keys(r).length>0)l={...l,...r};else{let e=i,s={...l},n=a.slice(),r=0;for(let e=0;e<a.length;e++){let t=a[e].trim();if(!t){r=e+1;continue}let l=t.match(/^(description|name|globs):\s*(.+)$/);if(l){let[,t,i]=l;s[t]=i.trim(),r=e+1}else break}if(e=n.slice(r).join("\n").trim()){let i=e.match(/^#\s+(.+)/m),a=i?i[1].trim():"Section ".concat(t.length+1);t.push({id:K(),title:a,content:e,...s}),l={}}}}return t.length>0?t:[{id:K(),title:"Section 1",content:e.trim()}]}function q(e){return 0===e.length?"":1!==e.length||e[0].description||e[0].name||e[0].globs?e.map(s=>{let t=[],l=s.description||s.name||s.globs;return(0!==e.indexOf(s)||l)&&t.push("---"),s.description&&t.push("description: ".concat(s.description)),s.name&&t.push("name: ".concat(s.name)),s.globs&&t.push("globs: ".concat(s.globs)),l&&t.push("---"),t.push(""),t.push(s.content),t.join("\n")}).join("\n\n"):e[0].content}function K(){return"section_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}function Z(e){let{section:s,index:t,totalSections:r,isExpanded:h,onUpdate:u,onDelete:m,onDuplicate:g,onMoveUp:p,onMoveDown:j,onToggleExpanded:v}=e,[y,f]=(0,i.useState)([]),b=(e,t)=>{let l={...s,[e]:t};u(l),f(function(e){let s=[];return e.title.trim()||s.push("Section title is required"),e.content.trim()||s.push("Section content is required"),s}(l))},N=function(e){let s=e.content.slice(0,100).replace(/\n/g," ");return s.length>100?"".concat(s,"..."):s}(s),w=y.length>0;return(0,l.jsxs)(d.Z,{className:w?"error-border":"",style:{transition:"all 0.2s ease"},children:[(0,l.jsxs)("div",{style:{paddingBottom:"var(--space-3)"},children:[(0,l.jsxs)(o.s,{justify:"between",align:"center",children:[(0,l.jsxs)(o.s,{align:"center",gap:"3",style:{flex:1},children:[(0,l.jsxs)(o.s,{align:"center",gap:"1",children:[(0,l.jsx)(U.A,{size:16,style:{color:"var(--gray-9)",cursor:"grab"}}),(0,l.jsx)(c.E,{size:"2",weight:"medium",style:{color:"var(--gray-9)"},children:t+1})]}),(0,l.jsxs)(n.a,{style:{flex:1},children:[(0,l.jsx)("div",{className:"font-semibold",style:{fontSize:"var(--font-size-3)"},children:s.title||"Section ".concat(t+1)}),!h&&(0,l.jsx)(c.E,{size:"2",style:{color:"var(--gray-9)",marginTop:"var(--space-1)"},truncate:!0,children:N})]})]}),(0,l.jsxs)(o.s,{align:"center",gap:"2",children:[(0,l.jsxs)(o.s,{align:"center",children:[(0,l.jsx)(a.$,{variant:"ghost",size:"1",onClick:p,disabled:0===t,style:{width:"32px",height:"32px",padding:0},children:(0,l.jsx)($.A,{size:16})}),(0,l.jsx)(a.$,{variant:"ghost",size:"1",onClick:j,disabled:t===r-1,style:{width:"32px",height:"32px",padding:0},children:(0,l.jsx)(B.A,{size:16})})]}),(0,l.jsxs)(F.Root,{children:[(0,l.jsx)(F.Trigger,{children:(0,l.jsx)(a.$,{variant:"ghost",size:"1",style:{width:"32px",height:"32px",padding:0},children:(0,l.jsx)(G.A,{size:16})})}),(0,l.jsxs)(F.Content,{align:"end",children:[(0,l.jsxs)(F.Item,{onClick:g,children:[(0,l.jsx)(_.A,{size:16,style:{marginRight:"var(--space-2)"}}),"Duplicate"]}),(0,l.jsxs)(F.Item,{onClick:m,disabled:1===r,className:"error-text",children:[(0,l.jsx)(M.A,{size:16,style:{marginRight:"var(--space-2)"}}),"Delete"]})]})]}),(0,l.jsx)(a.$,{variant:"ghost",size:"1",onClick:v,style:{width:"32px",height:"32px",padding:0},children:h?(0,l.jsx)($.A,{size:16}):(0,l.jsx)(B.A,{size:16})})]})]}),w&&(0,l.jsx)(n.a,{style:{marginTop:"var(--space-2)"},children:y.map((e,s)=>(0,l.jsxs)(c.E,{size:"2",className:"error-text",style:{display:"block"},children:["• ",e]},s))})]}),h&&(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"var(--space-4)"},children:[(0,l.jsxs)(n.a,{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"section-title-".concat(s.id),size:"2",weight:"medium",children:"Section Title"}),(0,l.jsx)(x.Root,{id:"section-title-".concat(s.id),value:s.title,onChange:e=>b("title",e.target.value),placeholder:"Enter section title..."})]}),(0,l.jsxs)(P.x,{columns:{initial:"1",md:"3"},gap:"4",children:[(0,l.jsxs)(n.a,{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"section-name-".concat(s.id),size:"2",weight:"medium",children:"Name (Optional)"}),(0,l.jsx)(x.Root,{id:"section-name-".concat(s.id),value:s.name||"",onChange:e=>b("name",e.target.value),placeholder:"e.g., global, stylesheet"})]}),(0,l.jsxs)(n.a,{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"section-globs-".concat(s.id),size:"2",weight:"medium",children:"File Patterns (Optional)"}),(0,l.jsx)(x.Root,{id:"section-globs-".concat(s.id),value:s.globs||"",onChange:e=>b("globs",e.target.value),placeholder:"e.g., **.css, *.js"})]}),(0,l.jsxs)(n.a,{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"section-description-".concat(s.id),size:"2",weight:"medium",children:"Description (Optional)"}),(0,l.jsx)(x.Root,{id:"section-description-".concat(s.id),value:s.description||"",onChange:e=>b("description",e.target.value),placeholder:"Brief description"})]})]}),(0,l.jsxs)(n.a,{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"section-content-".concat(s.id),size:"2",weight:"medium",children:"Content"}),(0,l.jsx)(A.B,{value:s.content,onChange:e=>b("content",e),placeholder:"Enter section content (markdown supported)...."})]})]})]})}function J(e){var s;let{sections:t,onSectionsChange:n}=e,[r,o]=(0,i.useState)(new Set(1===t.length?[null==(s=t[0])?void 0:s.id]:[])),d=()=>{var e;let s=(e=t.length,{id:K(),title:"Section ".concat(e+1),content:""});n([...t,s]),o(e=>new Set([...e,s.id]))},x=(e,s)=>{n(function(e,s,t){let l=[...e],[i]=l.splice(s,1);return l.splice(t,0,i),l}(t,e,s))};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(c.E,{className:"text-base font-medium",children:"Rule Sections"}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",t.length," section",1!==t.length?"s":"",")"]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[t.length>1&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(a.$,{variant:"ghost",size:"1",onClick:()=>{o(new Set([...t.map(e=>e.id)]))},className:"text-xs",children:"Expand All"}),(0,l.jsx)(a.$,{variant:"ghost",size:"1",onClick:()=>{o(new Set([]))},className:"text-xs",children:"Collapse All"})]}),(0,l.jsxs)(a.$,{variant:"outline",size:"1",onClick:d,className:"gap-2",children:[(0,l.jsx)(p.A,{className:"h-4 w-4"}),"Add Section"]})]})]}),(0,l.jsx)("div",{className:"space-y-3",children:0===t.length?(0,l.jsxs)("div",{className:"text-center py-12 border-2 border-dashed border-muted rounded-lg",children:[(0,l.jsx)(O.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No sections yet"}),(0,l.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add your first section to get started"}),(0,l.jsxs)(a.$,{onClick:d,className:"gap-2",children:[(0,l.jsx)(p.A,{className:"h-4 w-4"}),"Add Section"]})]}):t.map((e,s)=>(0,l.jsx)(Z,{section:e,index:s,totalSections:t.length,isExpanded:r.has(e.id),onUpdate:e=>((e,s)=>{let l=[...t];l[e]=s,n(l)})(s,e),onDelete:()=>(e=>{if(1===t.length)return;let s=t[e];n(t.filter((s,t)=>t!==e)),o(e=>{let t=new Set(e);return t.delete(s.id),t})})(s),onDuplicate:()=>(e=>{var s;let l={...s=t[e],id:K(),title:"".concat(s.title," (Copy)")},i=[...t];i.splice(e+1,0,l),n(i),o(e=>new Set([...e,l.id]))})(s),onMoveUp:()=>x(s,s-1),onMoveDown:()=>x(s,s+1),onToggleExpanded:()=>{var s;return s=e.id,void o(e=>{let t=new Set(e);return t.has(s)?t.delete(s):t.add(s),t})}},e.id))}),t.length>0&&(0,l.jsxs)("div",{className:"text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg",children:[(0,l.jsx)("p",{className:"font-medium mb-1",children:"Section Format:"}),(0,l.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,l.jsxs)("li",{children:["• Sections are separated by ",(0,l.jsx)("code",{children:"---"})," delimiters"]}),(0,l.jsxs)("li",{children:["• Optional metadata: ",(0,l.jsx)("code",{children:"description"}),", ",(0,l.jsx)("code",{children:"name"}),", ",(0,l.jsx)("code",{children:"globs"})]}),(0,l.jsx)("li",{children:"• Content supports markdown formatting"}),(0,l.jsx)("li",{children:"• Use file patterns (globs) to target specific file types"})]})]})]})}function W(e){let{rule:s,onSave:t,onCancel:n}=e,[r,d]=(0,i.useState)((null==s?void 0:s.title)||""),[m,g]=(0,i.useState)((null==s?void 0:s.description)||""),[v,y]=(0,i.useState)([]),[f,b]=(0,i.useState)((null==s?void 0:s.content)||""),[N,w]=(0,i.useState)("simple"),[R,z]=(0,i.useState)((null==s?void 0:s.ideType)||"GENERAL"),[S,T]=(0,i.useState)((null==s?void 0:s.visibility)||"PRIVATE"),[k,O]=(0,i.useState)((null==s?void 0:s.tags.map(e=>e.tag.name))||[]),[F,P]=(0,i.useState)("");(0,i.useEffect)(()=>{(null==s?void 0:s.content)?(y(V(s.content)),b(s.content)):(y([{id:"section_".concat(Date.now()),title:"Section 1",content:""}]),b(""))},[null==s?void 0:s.content]);let U=()=>{F.trim()&&!k.includes(F.trim())&&(O([...k,F.trim()]),P(""))};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"title",size:"2",weight:"medium",children:"Rule Title"}),(0,l.jsx)(x.Root,{id:"title",value:r,onChange:e=>d(e.target.value),placeholder:"Enter rule title..."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"description",size:"2",weight:"medium",children:"Description"}),(0,l.jsx)(C.f,{id:"description",value:m,onChange:e=>g(e.target.value),placeholder:"Describe what this rule does...",rows:3})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"ide-type",size:"2",weight:"medium",children:"IDE Type"}),(0,l.jsxs)(h.Root,{value:R,onValueChange:e=>z(e),children:[(0,l.jsx)(h.Trigger,{placeholder:"Select IDE type"}),(0,l.jsxs)(h.Content,{children:[(0,l.jsx)(h.Item,{value:"GENERAL",children:"General"}),(0,l.jsx)(h.Item,{value:"CURSOR",children:"Cursor"}),(0,l.jsx)(h.Item,{value:"AUGMENT",children:"Augment Code"}),(0,l.jsx)(h.Item,{value:"WINDSURF",children:"Windsurf"}),(0,l.jsx)(h.Item,{value:"CLAUDE",children:"Claude"}),(0,l.jsx)(h.Item,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,l.jsx)(h.Item,{value:"GEMINI",children:"Gemini"}),(0,l.jsx)(h.Item,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,l.jsx)(h.Item,{value:"CLINE",children:"Cline"}),(0,l.jsx)(h.Item,{value:"JUNIE",children:"Junie"}),(0,l.jsx)(h.Item,{value:"TRAE",children:"Trae"}),(0,l.jsx)(h.Item,{value:"LINGMA",children:"Lingma"}),(0,l.jsx)(h.Item,{value:"KIRO",children:"Kiro"}),(0,l.jsx)(h.Item,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.E,{as:"label",htmlFor:"visibility",size:"2",weight:"medium",children:"Visibility"}),(0,l.jsxs)(h.Root,{value:S,onValueChange:e=>T(e),children:[(0,l.jsx)(h.Trigger,{placeholder:"Select visibility"}),(0,l.jsxs)(h.Content,{children:[(0,l.jsx)(h.Item,{value:"PRIVATE",children:"Private"}),(0,l.jsx)(h.Item,{value:"PUBLIC",children:"Public"})]})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(c.E,{as:"label",size:"2",weight:"medium",children:"Tags"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:k.map(e=>(0,l.jsxs)(u.E,{variant:"soft",className:"gap-1",children:[e,(0,l.jsx)(a.$,{variant:"ghost",size:"1",className:"h-auto p-0 w-4 h-4",onClick:()=>{O(k.filter(s=>s!==e))},children:(0,l.jsx)(E.A,{className:"h-3 w-3"})})]},e))}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(x.Root,{value:F,onChange:e=>P(e.target.value),placeholder:"Add a tag...",onKeyPress:e=>"Enter"===e.key&&U()}),(0,l.jsx)(a.$,{onClick:U,size:"1",children:(0,l.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)(o.s,{justify:"between",align:"center",children:[(0,l.jsx)(c.E,{as:"label",size:"2",weight:"medium",children:"Rule Content"}),(0,l.jsxs)(D,{type:"single",value:N,onValueChange:e=>e&&void(e!==N&&("simple"===e?b(q(v)):y(V(f)),w(e))),className:"bg-gray-50 dark:bg-gray-800 rounded-md p-1",children:[(0,l.jsxs)(L,{value:"simple",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[(0,l.jsx)(j.A,{className:"h-4 w-4"}),"Simple"]}),(0,l.jsxs)(L,{value:"advanced",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[(0,l.jsx)(I.A,{className:"h-4 w-4"}),"Advanced"]})]})]}),"simple"===N?(0,l.jsxs)("div",{children:[(0,l.jsx)(c.E,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content in a simple code editor format"}),(0,l.jsx)(A.B,{value:f,onChange:e=>{b(e),"simple"===N&&y(V(e))},placeholder:"Enter your rule content here...",className:"min-h-[300px]"})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)(c.E,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content using structured sections with metadata"}),(0,l.jsx)(J,{sections:v,onSectionsChange:e=>{y(e),"advanced"===N&&b(q(e))}})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,l.jsx)(a.$,{variant:"outline",onClick:n,children:"Cancel"}),(0,l.jsx)(a.$,{onClick:()=>{t({title:r,description:m,content:"simple"===N?f:q(v),ideType:R,visibility:S,tags:k})},children:s?"Update Rule":"Create Rule"})]})]})}var H=t(67642),Y=t(51874),Q=t(58124),X=t(73297),ee=t(24772);let es=async()=>{let e=await fetch("/api/tags");if(!e.ok)throw Error("Failed to fetch tags");return e.json()},et="force-dynamic";function el(){let{data:e}=(0,H.wV)(),[s,t]=(0,i.useState)(""),[C,E]=(0,i.useState)([]),[I,A]=(0,i.useState)("ALL"),[R,z]=(0,i.useState)(!1),[S,T]=(0,i.useState)(null),k=(0,i.useMemo)(()=>({search:s||void 0,tags:C.length>0?C:void 0,ideType:"ALL"!==I?I:void 0}),[s,C,I]),{data:D,isLoading:L,error:O}=(0,Q.fs)(k),{data:F=[],isLoading:P}=(0,X.I)({queryKey:ee.l.tags.lists(),queryFn:es,staleTime:6e5}),U=(0,Q.El)(),$=(null==D?void 0:D.rules)||[];(0,i.useEffect)(()=>{console.log("Dialog state changed:",R)},[R]),(0,i.useEffect)(()=>{let e=e=>{"Escape"===e.key&&R&&z(!1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[R]),(0,i.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll("*"),s=[];e.forEach(e=>{let t=window.getComputedStyle(e),l=e.getBoundingClientRect();"fixed"===t.position&&l.width>=.9*window.innerWidth&&l.height>=.9*window.innerHeight&&"none"!==t.pointerEvents&&s.push(e)}),s.length>0&&(console.warn("Found blocking elements:",s),s.forEach(e=>{console.log("Blocking element:",{element:e,classes:e.className,id:e.id,zIndex:window.getComputedStyle(e).zIndex,display:window.getComputedStyle(e).display})}))};e();let s=setTimeout(e,1e3);return()=>clearTimeout(s)},[]),(0,i.useEffect)(()=>{O&&(console.error("Error fetching rules:",O),Y.oR.error("Failed to fetch rules"))},[O]);let B=()=>{T(null),z(!0)},G=e=>{T(e),z(!0)},_=async e=>{try{let s=S?"PUT":"POST",t=S?"/api/rules/".concat(S.id):"/api/rules";(await fetch(t,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?(Y.oR.success(S?"Rule updated":"Rule created"),z(!1),T(null),U()):Y.oR.error("Failed to save rule")}catch(e){console.error("Error saving rule:",e),Y.oR.error("Failed to save rule")}},M=async e=>{try{(await fetch("/api/rules/".concat(e),{method:"DELETE"})).ok?(Y.oR.success("Rule deleted"),U()):Y.oR.error("Failed to delete rule")}catch(e){console.error("Error deleting rule:",e),Y.oR.error("Failed to delete rule")}};if(!(null==e?void 0:e.user))return(0,l.jsx)("div",{className:"container py-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold mb-4",style:{color:"hsl(0 0% 98%)"},children:"Please sign in to continue"}),(0,l.jsx)(a.$,{asChild:!0,children:(0,l.jsx)("a",{href:"/auth/signin",children:"Sign In"})})]})});let V=e||null,q=Array.isArray($)?$.filter(e=>{var s;return e.userId===(null==V||null==(s=V.user)?void 0:s.id)}):[],K=Array.isArray($)?$.filter(e=>"PUBLIC"===e.visibility):[],Z={totalRules:q.length,publicRules:q.filter(e=>"PUBLIC"===e.visibility).length,privateRules:q.filter(e=>"PRIVATE"===e.visibility).length,totalViews:0};return L&&!$.length?(0,l.jsx)(n.a,{className:"mobile-container py-6 xs:py-8 space-y-6 xs:space-y-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(r.D,{size:"6","xs-size":"8",weight:"bold",color:"gray",highContrast:!0,className:"text-2xl xs:text-3xl",children:"Dashboard"}),(0,l.jsx)(c.E,{size:"2","xs-size":"3",color:"gray",className:"text-sm xs:text-base mb-8",children:"Loading your rules..."}),(0,l.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 xs:gap-4",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,l.jsx)("div",{className:"bg-gray-200 dark:bg-gray-700 rounded-lg h-20"},s))}),(0,l.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,l.jsx)("div",{className:"bg-gray-200 dark:bg-gray-700 rounded-lg h-32"},s))})]})]})}):(0,l.jsxs)(n.a,{className:"mobile-container py-6 xs:py-8 space-y-6 xs:space-y-8",children:[(0,l.jsxs)(o.s,{justify:"between",align:"center",className:"flex-col xs:flex-row gap-4 xs:gap-0",children:[(0,l.jsxs)(n.a,{className:"text-center xs:text-left",children:[(0,l.jsx)(r.D,{size:"6","xs-size":"8",weight:"bold",color:"gray",highContrast:!0,className:"text-2xl xs:text-3xl",children:"Dashboard"}),(0,l.jsx)(c.E,{size:"2","xs-size":"3",color:"gray",className:"text-sm xs:text-base",children:"Manage your AI prompt rules and explore the community"})]}),(0,l.jsxs)(a.$,{onClick:B,className:"touch-target w-full xs:w-auto",children:[(0,l.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"New Rule"]})]}),(0,l.jsxs)(n.a,{className:"grid grid-cols-2 md:grid-cols-4 gap-3 xs:gap-4",children:[(0,l.jsxs)(d.Z,{className:"mobile-card",children:[(0,l.jsxs)(o.s,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(c.E,{size:"1","xs-size":"2",weight:"medium",color:"gray",highContrast:!0,className:"text-xs xs:text-sm",children:"Total Rules"}),(0,l.jsx)(j.A,{className:"h-3 w-3 xs:h-4 xs:w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(c.E,{size:"5","xs-size":"7",weight:"bold",color:"gray",highContrast:!0,className:"text-xl xs:text-2xl",children:Z.totalRules})]}),(0,l.jsxs)(d.Z,{className:"mobile-card",children:[(0,l.jsxs)(o.s,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(c.E,{size:"1","xs-size":"2",weight:"medium",color:"gray",highContrast:!0,className:"text-xs xs:text-sm",children:"Public Rules"}),(0,l.jsx)(v.A,{className:"h-3 w-3 xs:h-4 xs:w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(c.E,{size:"5","xs-size":"7",weight:"bold",color:"gray",highContrast:!0,className:"text-xl xs:text-2xl",children:Z.publicRules})]}),(0,l.jsxs)(d.Z,{className:"mobile-card",children:[(0,l.jsxs)(o.s,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(c.E,{size:"1","xs-size":"2",weight:"medium",color:"gray",highContrast:!0,className:"text-xs xs:text-sm",children:"Private Rules"}),(0,l.jsx)(y.A,{className:"h-3 w-3 xs:h-4 xs:w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(c.E,{size:"5","xs-size":"7",weight:"bold",color:"gray",highContrast:!0,className:"text-xl xs:text-2xl",children:Z.privateRules})]}),(0,l.jsxs)(d.Z,{className:"mobile-card",children:[(0,l.jsxs)(o.s,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(c.E,{size:"1","xs-size":"2",weight:"medium",color:"gray",highContrast:!0,className:"text-xs xs:text-sm",children:"Total Views"}),(0,l.jsx)(f.A,{className:"h-3 w-3 xs:h-4 xs:w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(c.E,{size:"5","xs-size":"7",weight:"bold",color:"gray",highContrast:!0,className:"text-xl xs:text-2xl",children:Z.totalViews})]})]}),(0,l.jsxs)("div",{className:"mobile-flex gap-3 xs:gap-4",children:[(0,l.jsxs)("div",{className:"relative flex-1",children:[(0,l.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(x.Root,{placeholder:"Search rules...",value:s,onChange:e=>t(e.target.value),className:"pl-10 h-11 xs:h-12 text-sm xs:text-base"})]}),(0,l.jsxs)(h.Root,{value:I,onValueChange:A,children:[(0,l.jsx)(h.Trigger,{className:"w-full sm:w-40 md:w-48 h-11 xs:h-12 text-sm xs:text-base",placeholder:"IDE Type"}),(0,l.jsxs)(h.Content,{children:[(0,l.jsx)(h.Item,{value:"ALL",children:"All IDEs"}),(0,l.jsx)(h.Item,{value:"GENERAL",children:"General"}),(0,l.jsx)(h.Item,{value:"CURSOR",children:"Cursor"}),(0,l.jsx)(h.Item,{value:"AUGMENT",children:"Augment Code"}),(0,l.jsx)(h.Item,{value:"WINDSURF",children:"Windsurf"}),(0,l.jsx)(h.Item,{value:"CLAUDE",children:"Claude"}),(0,l.jsx)(h.Item,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,l.jsx)(h.Item,{value:"GEMINI",children:"Gemini"}),(0,l.jsx)(h.Item,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,l.jsx)(h.Item,{value:"CLINE",children:"Cline"}),(0,l.jsx)(h.Item,{value:"JUNIE",children:"Junie"}),(0,l.jsx)(h.Item,{value:"TRAE",children:"Trae"}),(0,l.jsx)(h.Item,{value:"LINGMA",children:"Lingma"}),(0,l.jsx)(h.Item,{value:"KIRO",children:"Kiro"}),(0,l.jsx)(h.Item,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),F.length>0&&(0,l.jsxs)(n.a,{className:"space-y-3 xs:space-y-4",children:[(0,l.jsxs)(o.s,{align:"center",gap:"2",children:[(0,l.jsx)(N.A,{className:"h-4 w-4",style:{color:"var(--gray-12)"}}),(0,l.jsx)(c.E,{size:"2",weight:"medium",color:"gray",highContrast:!0,className:"text-sm xs:text-base",children:"Filter by tags:"})]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:F.map(e=>(0,l.jsx)(u.E,{variant:C.includes(e.name)?"solid":"outline",className:"cursor-pointer touch-target text-xs xs:text-sm px-2 xs:px-3 py-1 xs:py-1.5",onClick:()=>{var s;return s=e.name,void E(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s])},style:{borderColor:e.color,backgroundColor:C.includes(e.name)?e.color:"transparent"},children:e.name},e.id))})]}),(0,l.jsxs)(m.bL,{defaultValue:"my-rules",className:"space-y-4 xs:space-y-6",children:[(0,l.jsxs)(m.B8,{className:"w-full",children:[(0,l.jsxs)(m.l9,{value:"my-rules",className:"flex-1 text-sm xs:text-base",children:["My Rules (",q.length,")"]}),(0,l.jsxs)(m.l9,{value:"community",className:"flex-1 text-sm xs:text-base",children:["Community (",K.length,")"]})]}),(0,l.jsx)(m.UC,{value:"my-rules",className:"space-y-4 xs:space-y-6",children:0===q.length?(0,l.jsxs)(d.Z,{className:"py-8 xs:py-12 text-center mobile-card",children:[(0,l.jsx)(j.A,{className:"h-8 w-8 xs:h-12 xs:w-12 text-muted-foreground mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-base xs:text-lg font-semibold mb-2",children:"No rules yet"}),(0,l.jsx)("p",{className:"text-muted-foreground mb-4 text-sm xs:text-base px-4",children:"Create your first AI prompt rule to get started"}),(0,l.jsxs)(a.$,{onClick:B,className:"touch-target",children:[(0,l.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Create Rule"]})]}):(0,l.jsx)("div",{className:"mobile-grid",children:q.map(e=>(0,l.jsx)(w.z,{rule:e,onEdit:G,onDelete:M,isOwner:!0},e.id))})}),(0,l.jsx)(m.UC,{value:"community",className:"space-y-4 xs:space-y-6",children:0===K.length?(0,l.jsxs)(d.Z,{className:"py-8 xs:py-12 text-center mobile-card",children:[(0,l.jsx)(v.A,{className:"h-8 w-8 xs:h-12 xs:w-12 text-muted-foreground mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-base xs:text-lg font-semibold mb-2",children:"No community rules found"}),(0,l.jsx)("p",{className:"text-muted-foreground text-sm xs:text-base px-4",children:"Try adjusting your filters or check back later"})]}):(0,l.jsx)("div",{className:"mobile-grid",children:K.map(e=>{var s;return(0,l.jsx)(w.z,{rule:e,isOwner:e.userId===(null==V||null==(s=V.user)?void 0:s.id)},e.id)})})})]}),(0,l.jsx)(g.bL,{open:R,onOpenChange:z,children:(0,l.jsxs)(g.UC,{className:"max-w-4xl max-h-[90vh] w-[95vw] xs:w-[90vw] overflow-hidden",children:[(0,l.jsx)(g.hE,{className:"text-lg xs:text-xl",children:S?"Edit Rule":"Create New Rule"}),(0,l.jsx)("div",{className:"overflow-y-auto max-h-[70vh]",children:(0,l.jsx)(W,{rule:S||void 0,onSave:_,onCancel:()=>{z(!1),T(null)}})})]})})]})}}},e=>{e.O(0,[7401,1065,795,7261,8560,8916,2953,7610,4826,3096,3243,3297,9428,2461,587,1902,7358],()=>e(e.s=24237)),_N_E=e.O()}]);