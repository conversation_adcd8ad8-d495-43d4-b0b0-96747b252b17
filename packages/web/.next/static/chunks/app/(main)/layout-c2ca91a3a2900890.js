(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2076],{7321:(e,s,t)=>{"use strict";t.d(s,{VG:()=>l,in:()=>n});var a=t(79924),r=t(25474);let l={CURSOR:{name:"<PERSON>urs<PERSON>",description:"AI-powered code editor",command:"cursor"},AUGMENT:{name:"Augment",description:"AI coding assistant",command:"augment"},WINDSURF:{name:"Windsurf",description:"AI development environment",command:"windsurf"},CLAUDE:{name:"<PERSON>",description:"Anthropic AI assistant",command:"claude"},GITHUB_COPILOT:{name:"GitHub Copilot",description:"AI pair programmer",command:"github-copilot"},GEMINI:{name:"Gemini",description:"Google AI assistant",command:"gemini"},OPENAI_CODEX:{name:"OpenAI Codex",description:"OpenAI code assistant",command:"openai-codex"},CLINE:{name:"<PERSON><PERSON>",description:"AI coding assistant",command:"cline"},JUNIE:{name:"<PERSON>ie",description:"AI development tool",command:"junie"},TRAE:{name:"Trae",description:"AI coding companion",command:"trae"},LINGMA:{name:"Lingma",description:"AI programming assistant",command:"lingma"},KIRO:{name:"Kiro",description:"AI development environment",command:"kiro"},TENCENT_CODEBUDDY:{name:"Tencent CodeBuddy",description:"Tencent AI coding assistant",command:"tencent-codebuddy"},GENERAL:{name:"General",description:"General purpose IDE",command:"general"}};(0,a.eU)([]),(0,a.eU)([]),(0,a.eU)(null),(0,a.eU)(""),(0,a.eU)([]),(0,a.eU)("ALL"),(0,r.tG)("theme","system");let n=(0,r.tG)("ide-preferences",{preferredIDEs:[],defaultIDE:void 0});(0,a.eU)(e=>e(n).preferredIDEs,(e,s,t)=>{let a=e(n);s(n,{...a,preferredIDEs:t})}),(0,a.eU)(e=>{let s=e(n);return s.preferredIDEs.find(e=>e.id===s.defaultIDE)},(e,s,t)=>{let a=e(n);s(n,{...a,defaultIDE:t})})},12210:(e,s,t)=>{"use strict";t.d(s,{QueryProvider:()=>n});var a=t(54568),r=t(87606),l=t(24772);function n(e){let{children:s}=e;return(0,a.jsxs)(r.Ht,{client:l.q,children:[s,!1]})}},12463:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13100:(e,s,t)=>{"use strict";e.exports=t(61907)},23469:(e,s,t)=>{"use strict";t.d(s,{JotaiProvider:()=>l});var a=t(54568),r=t(60844);function l(e){let{children:s}=e;return(0,a.jsx)(r.Kq,{children:s})}},24508:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},24772:(e,s,t)=>{"use strict";t.d(s,{l:()=>n,q:()=>l});var a=t(78021),r=t(51874);let l=new a.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,s)=>(!((null==s?void 0:s.status)>=400)||!((null==s?void 0:s.status)<500))&&e<3,refetchOnWindowFocus:!1,throwOnError:!1},mutations:{retry:!1,onError:e=>{console.error("Mutation error:",e);let s=(null==e?void 0:e.message)||"An error occurred";r.oR.error(s)}}}}),n={rules:{all:["rules"],lists:()=>[...n.rules.all,"list"],list:e=>[...n.rules.lists(),e],details:()=>[...n.rules.all,"detail"],detail:e=>[...n.rules.details(),e],raw:e=>[...n.rules.all,"raw",e]},rulesets:{all:["rulesets"],lists:()=>[...n.rulesets.all,"list"],list:e=>[...n.rulesets.lists(),e],details:()=>[...n.rulesets.all,"detail"],detail:e=>[...n.rulesets.details(),e]},tags:{all:["tags"],lists:()=>[...n.tags.all,"list"]},user:{all:["user"],profile:e=>[...n.user.all,"profile",e]}}},26948:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Avatar:()=>p,AvatarFallback:()=>j,AvatarImage:()=>g,Fallback:()=>b,Image:()=>k,Root:()=>N,createAvatarScope:()=>u});var a=t(7620),r=t(80482),l=t(93568),n=t(17247),i=t(7156),c=t(66165),d=t(54568),o="Avatar",[m,u]=(0,r.A)(o),[h,x]=m(o),p=a.forwardRef((e,s)=>{let{__scopeAvatar:t,...r}=e,[l,n]=a.useState("idle");return(0,d.jsx)(h,{scope:t,imageLoadingStatus:l,onImageLoadingStatusChange:n,children:(0,d.jsx)(i.sG.span,{...r,ref:s})})});p.displayName=o;var f="AvatarImage",g=a.forwardRef((e,s)=>{let{__scopeAvatar:t,src:r,onLoadingStatusChange:o=()=>{},...m}=e,u=x(f,t),h=function(e,s){let{referrerPolicy:t,crossOrigin:r}=s,l=(0,c.z)(),i=a.useRef(null),d=l?(i.current||(i.current=new window.Image),i.current):null,[o,m]=a.useState(()=>y(d,e));return(0,n.N)(()=>{m(y(d,e))},[d,e]),(0,n.N)(()=>{let e=e=>()=>{m(e)};if(!d)return;let s=e("loaded"),a=e("error");return d.addEventListener("load",s),d.addEventListener("error",a),t&&(d.referrerPolicy=t),"string"==typeof r&&(d.crossOrigin=r),()=>{d.removeEventListener("load",s),d.removeEventListener("error",a)}},[d,r,t]),o}(r,m),p=(0,l.c)(e=>{o(e),u.onImageLoadingStatusChange(e)});return(0,n.N)(()=>{"idle"!==h&&p(h)},[h,p]),"loaded"===h?(0,d.jsx)(i.sG.img,{...m,ref:s,src:r}):null});g.displayName=f;var v="AvatarFallback",j=a.forwardRef((e,s)=>{let{__scopeAvatar:t,delayMs:r,...l}=e,n=x(v,t),[c,o]=a.useState(void 0===r);return a.useEffect(()=>{if(void 0!==r){let e=window.setTimeout(()=>o(!0),r);return()=>window.clearTimeout(e)}},[r]),c&&"loaded"!==n.imageLoadingStatus?(0,d.jsx)(i.sG.span,{...l,ref:s}):null});function y(e,s){return e?s?(e.src!==s&&(e.src=s),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}j.displayName=v;var N=p,k=g,b=j},44740:(e,s,t)=>{"use strict";t.d(s,{Avatar:()=>x});var a=t(7620),r=t(77785),l=t(26948),n=t(74531),i=t(33233),c=t(63135),d=t(88796);let o={...n.f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],default:"3",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["solid","soft"],default:"soft"},...i.un,...c.Z,...d.F,fallback:{type:"ReactNode",required:!0}};var m=t(55772),u=t(78273),h=t(45225);let x=a.forwardRef((e,s)=>{let{asChild:t,children:n,className:i,style:c,color:d,radius:x,...f}=(0,m.o)(e,o,h.y);return a.createElement(l.Root,{"data-accent-color":d,"data-radius":x,className:r("rt-reset","rt-AvatarRoot",i),style:c,asChild:t},(0,u.T)({asChild:t,children:n},a.createElement(p,{ref:s,...f})))});x.displayName="Avatar";let p=a.forwardRef((e,s)=>{let{fallback:t,...n}=e,[i,c]=a.useState("idle");return a.createElement(a.Fragment,null,"idle"===i||"loading"===i?a.createElement("span",{className:"rt-AvatarFallback"}):null,"error"===i?a.createElement(l.Fallback,{className:r("rt-AvatarFallback",{"rt-one-letter":"string"==typeof t&&1===t.length,"rt-two-letters":"string"==typeof t&&2===t.length}),delayMs:0},t):null,a.createElement(l.Image,{ref:s,className:"rt-AvatarImage",...n,onLoadingStatusChange:e=>{var s;null==(s=n.onLoadingStatusChange)||s.call(n,e),c(e)}}))});p.displayName="AvatarImpl"},48253:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,27261,23)),Promise.resolve().then(t.bind(t,98920)),Promise.resolve().then(t.bind(t,23469)),Promise.resolve().then(t.bind(t,12210)),Promise.resolve().then(t.bind(t,93179)),Promise.resolve().then(t.bind(t,83969))},61907:(e,s,t)=>{"use strict";var a=t(7620),r="function"==typeof Object.is?Object.is:function(e,s){return e===s&&(0!==e||1/e==1/s)||e!=e&&s!=s},l=a.useState,n=a.useEffect,i=a.useLayoutEffect,c=a.useDebugValue;function d(e){var s=e.getSnapshot;e=e.value;try{var t=s();return!r(e,t)}catch(e){return!0}}var o="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,s){return s()}:function(e,s){var t=s(),a=l({inst:{value:t,getSnapshot:s}}),r=a[0].inst,o=a[1];return i(function(){r.value=t,r.getSnapshot=s,d(r)&&o({inst:r})},[e,t,s]),n(function(){return d(r)&&o({inst:r}),e(function(){d(r)&&o({inst:r})})},[e]),c(t),t};s.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:o},64529:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},66165:(e,s,t)=>{"use strict";t.d(s,{z:()=>r});var a=t(13100);function r(){return(0,a.useSyncExternalStore)(l,()=>!0,()=>!1)}function l(){return()=>{}}},67642:(e,s,t)=>{"use strict";t.d(s,{CI:()=>l,Jv:()=>a,wV:()=>n});let{signIn:a,signUp:r,signOut:l,useSession:n,getSession:i}=(0,t(57664).MB)({baseURL:"https://onlyrules.codes"})},68262:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},83969:(e,s,t)=>{"use strict";t.d(s,{Toaster:()=>n});var a=t(54568),r=t(68309),l=t(51874);let n=e=>{let{...s}=e,{theme:t="system"}=(0,r.D)();return(0,a.jsx)(l.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...s})}},85962:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},93179:(e,s,t)=>{"use strict";t.d(s,{ThemeProvider:()=>c});var a=t(54568),r=t(7620),l=t(68309),n=t(37834);function i(e){let{children:s}=e,{theme:t,systemTheme:i}=(0,l.D)(),[c,d]=r.useState(!1);return(r.useEffect(()=>{d(!0)},[]),c)?(0,a.jsx)(n.Theme,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:("system"===t?i||"dark":t)||"dark",panelBackground:"translucent",hasBackground:!1,children:s}):(0,a.jsx)(n.Theme,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:"dark",panelBackground:"translucent",hasBackground:!1,children:s})}function c(e){let{children:s,...t}=e;return(0,a.jsx)(l.N,{defaultTheme:"dark",...t,children:(0,a.jsx)(i,{children:s})})}},96238:(e,s,t)=>{var a={"./en/messages.js":[41571,1571],"./zh-CN/messages.js":[42368,2368],"./zh-HK/messages.js":[30228,228]};function r(e){if(!t.o(a,e))return Promise.resolve().then(()=>{var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s});var s=a[e],r=s[0];return t.e(s[1]).then(()=>t.t(r,23))}r.keys=()=>Object.keys(a),r.id=96238,e.exports=r},98920:(e,s,t)=>{"use strict";t.d(s,{ClientNavbar:()=>G});var a=t(54568),r=t(27261),l=t.n(r),n=t(7620),i=t(68309),c=t(48976),d=t(85962),o=t(98889);let m=(0,o.A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),u=(0,o.A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),h=(0,o.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var x=t(24508);let p=(0,o.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var f=t(12463);let g=(0,o.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var v=t(61065),j=t(69244),y=t(44740),N=t(67642),k=t(58983);let b={en:"English","zh-CN":"简体中文","zh-HK":"繁體中文"},w={en:"EN","zh-CN":"简","zh-HK":"繁"};function E(e){let{currentLocale:s}=e,[t,r]=(0,n.useTransition)(),[l,i]=(0,n.useState)(s);return(0,a.jsxs)(k.Root,{value:l,onValueChange:e=>{i(e),r(()=>{document.cookie="locale=".concat(e,";path=/;max-age=").concat(31536e3),window.location.reload()})},disabled:t,children:[(0,a.jsx)(k.Trigger,{className:"w-[60px] px-2 h-8 text-xs",placeholder:w[l]}),(0,a.jsx)(k.Content,{children:Object.entries(w).map(e=>{let[s,t]=e;return(0,a.jsxs)(k.Item,{value:s,children:[(0,a.jsx)("span",{className:"text-xs",children:t}),(0,a.jsx)("span",{className:"ml-2 text-xs text-muted-foreground",children:b[s]})]},s)})})]})}var I=t(60844),A=t(34964),C=t(61689),D=t(64529),S=t(13185);let O=(0,o.A)("StarOff",[["path",{d:"M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43",key:"16m0ql"}],["path",{d:"M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91",key:"1vt8nq"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);var T=t(68262),L=t(7321),M=t(51874);function z(e){let{open:s,onOpenChange:t}=e,[r,l]=(0,I.fp)(L.in),[i,d]=(0,n.useState)(""),o=Object.entries(L.VG).filter(e=>{let[s]=e;return!r.preferredIDEs.some(e=>e.type===s)});return(0,a.jsx)(A.bL,{open:s,onOpenChange:t,children:(0,a.jsxs)(A.UC,{className:"max-w-2xl",children:[(0,a.jsxs)(A.hE,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"IDE Preferences"]}),(0,a.jsx)(A.VY,{children:"Manage your preferred IDEs for quick command generation. Set a default IDE and add your favorites."}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium",children:"Add IDE"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(k.Root,{value:i,onValueChange:e=>d(e),children:[(0,a.jsx)(k.Trigger,{className:"flex-1",placeholder:"Select an IDE to add..."}),(0,a.jsx)(k.Content,{children:o.map(e=>{let[s,t]=e;return(0,a.jsx)(k.Item,{value:s,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:t.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:t.description})]})]})},s)})})]}),(0,a.jsxs)(v.$,{onClick:()=>{if(!i)return;let e={id:"".concat(i,"-").concat(Date.now()),name:L.VG[i].name,type:i,isDefault:0===r.preferredIDEs.length,addedAt:new Date().toISOString()};l({...r,preferredIDEs:[...r.preferredIDEs,e],defaultIDE:0===r.preferredIDEs.length?e.id:r.defaultIDE}),d(""),M.oR.success("".concat(L.VG[i].name," added to preferences"))},disabled:!i,size:"2",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 mr-1"}),"Add"]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["Preferred IDEs (",r.preferredIDEs.length,")"]}),0===r.preferredIDEs.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No preferred IDEs added yet"}),(0,a.jsx)("p",{className:"text-xs",children:"Add your favorite IDEs to generate quick commands"})]}):(0,a.jsx)("div",{className:"space-y-2",children:r.preferredIDEs.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),r.defaultIDE===e.id&&(0,a.jsx)(C.E,{variant:"soft",className:"text-xs",children:"Default"})]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:L.VG[e.type].description})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(v.$,{variant:"ghost",size:"2",onClick:()=>(e=>{let s=r.preferredIDEs.find(s=>s.id===e);l({...r,defaultIDE:e}),s&&M.oR.success("".concat(s.name," set as default IDE"))})(e.id),disabled:r.defaultIDE===e.id,className:"h-8 w-8 p-0",children:r.defaultIDE===e.id?(0,a.jsx)(S.A,{className:"h-4 w-4 fill-current"}):(0,a.jsx)(O,{className:"h-4 w-4"})}),(0,a.jsx)(v.$,{variant:"ghost",size:"2",onClick:()=>(e=>{let s=r.preferredIDEs.find(s=>s.id===e),t=r.preferredIDEs.filter(s=>s.id!==e),a=r.defaultIDE;r.defaultIDE===e&&(a=t.length>0?t[0].id:void 0),l({preferredIDEs:t,defaultIDE:a}),s&&M.oR.success("".concat(s.name," removed from preferences"))})(e.id),className:"h-8 w-8 p-0 text-destructive hover:text-destructive",children:(0,a.jsx)(T.A,{className:"h-4 w-4"})})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground bg-muted p-3 rounded-lg",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"How it works:"}),(0,a.jsxs)("ul",{className:"space-y-1",children:[(0,a.jsx)("li",{children:"• Add your favorite IDEs to generate quick npx commands"}),(0,a.jsx)("li",{children:"• Set a default IDE for one-click command copying"}),(0,a.jsx)("li",{children:"• Commands will include the --target flag for your selected IDE"})]})]})]})]})})}function R(e){var s,t,r,o;let{locale:k}=e,{theme:b,setTheme:w}=(0,i.D)(),{data:I}=(0,N.wV)(),[A,C]=(0,n.useState)(!1),[D,S]=(0,n.useState)(!1),[O,T]=(0,n.useState)(!1),L=async()=>{await (0,N.CI)(),S(!1),T(!1)},M=()=>{C(!0),S(!1),T(!1)},R=()=>{S(!1),T(!1)},G=[{href:"/dashboard",label:"Dashboard"},{href:"/templates",label:"Templates"},{href:"/ides",label:"IDEs"},{href:"/tutorials",label:"Tutorials"},{href:"/docs",label:"Docs"},{href:"/shared",label:"Community"}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("nav",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:[(0,a.jsxs)("div",{className:"mobile-container flex h-14 xs:h-16 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 xs:gap-8",children:[(0,a.jsxs)(l(),{href:"/",className:"flex items-center gap-2",onClick:R,children:[(0,a.jsx)(c.A,{className:"h-5 w-5 xs:h-6 xs:w-6 text-primary"}),(0,a.jsx)("span",{className:"font-bold text-lg xs:text-xl",children:"OnlyRules"})]}),(0,a.jsx)("nav",{className:"hidden md:flex items-center gap-6",children:G.map(e=>(0,a.jsx)(l(),{href:e.href,className:"text-sm font-medium transition-colors hover:text-primary",onClick:R,children:e.label},e.href))})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 xs:gap-4",children:[(0,a.jsxs)("div",{className:"hidden xs:flex items-center gap-2 xs:gap-3",children:[(0,a.jsx)(E,{currentLocale:k}),(0,a.jsx)(v.$,{variant:"ghost",size:"2",asChild:!0,className:"touch-target",children:(0,a.jsxs)(l(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"GitHub"})]})}),(0,a.jsxs)(v.$,{variant:"ghost",size:"2",onClick:()=>w("dark"===b?"light":"dark"),className:"touch-target",children:[(0,a.jsx)(m,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(u,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}),(null==I?void 0:I.user)?(0,a.jsxs)(j.Root,{open:D,onOpenChange:S,children:[(0,a.jsx)(j.Trigger,{children:(0,a.jsx)(v.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full touch-target",children:(0,a.jsx)(y.Avatar,{className:"h-8 w-8",src:I.user.image||"",fallback:(null==(s=I.user.name)?void 0:s.charAt(0))||(null==(t=I.user.email)?void 0:t.charAt(0))||"U"})})}),(0,a.jsxs)(j.Content,{className:"w-56",align:"end",children:[(0,a.jsx)("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[I.user.name&&(0,a.jsx)("p",{className:"font-medium text-sm",children:I.user.name}),I.user.email&&(0,a.jsx)("p",{className:"w-[200px] truncate text-xs text-muted-foreground",children:I.user.email})]})}),(0,a.jsx)(j.Separator,{}),(0,a.jsx)(j.Item,{asChild:!0,children:(0,a.jsxs)(l(),{href:"/dashboard",onClick:R,className:"text-sm",children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,a.jsxs)(j.Item,{onClick:M,className:"text-sm",children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"IDE Preferences"]}),(0,a.jsx)(j.Item,{asChild:!0,children:(0,a.jsxs)(l(),{href:"/settings",onClick:R,className:"text-sm",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Settings"]})}),(0,a.jsx)(j.Separator,{}),(0,a.jsxs)(j.Item,{onClick:L,className:"text-sm",children:[(0,a.jsx)(p,{className:"mr-2 h-4 w-4"}),"Sign Out"]})]})]}):(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(v.$,{variant:"ghost",size:"2",className:"touch-target",children:(0,a.jsx)(l(),{href:"/auth/signin",onClick:R,children:"Sign In"})})})]}),(0,a.jsx)(v.$,{variant:"ghost",size:"2",onClick:()=>{T(!O)},className:"xs:hidden touch-target","aria-label":"Toggle mobile menu",children:O?(0,a.jsx)(f.A,{className:"h-5 w-5"}):(0,a.jsx)(g,{className:"h-5 w-5"})})]})]}),O&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40 xs:hidden",onClick:()=>T(!1)}),(0,a.jsx)("div",{className:"\n          fixed top-14 left-0 right-0 bg-background border-b shadow-lg z-50 xs:hidden\n          transform transition-transform duration-300 ease-in-out\n          ".concat(O?"translate-y-0":"-translate-y-full","\n        "),children:(0,a.jsxs)("div",{className:"mobile-container py-4 space-y-4",children:[(0,a.jsx)("nav",{className:"space-y-3",children:G.map(e=>(0,a.jsx)(l(),{href:e.href,className:"block py-2 text-sm font-medium transition-colors hover:text-primary touch-target",onClick:R,children:e.label},e.href))}),(0,a.jsxs)("div",{className:"border-t pt-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Language"}),(0,a.jsx)(E,{currentLocale:k})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Theme"}),(0,a.jsxs)(v.$,{variant:"ghost",size:"2",onClick:()=>w("dark"===b?"light":"dark"),className:"touch-target",children:[(0,a.jsx)(m,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(u,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"ml-2 text-sm",children:"dark"===b?"Light":"Dark"})]})]}),(0,a.jsxs)(l(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target",onClick:R,children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"GitHub"]}),(null==I?void 0:I.user)?(0,a.jsxs)("div",{className:"border-t pt-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 py-2",children:[(0,a.jsx)(y.Avatar,{className:"h-8 w-8",src:I.user.image||"",fallback:(null==(r=I.user.name)?void 0:r.charAt(0))||(null==(o=I.user.email)?void 0:o.charAt(0))||"U"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[I.user.name&&(0,a.jsx)("p",{className:"font-medium text-sm truncate",children:I.user.name}),I.user.email&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:I.user.email})]})]}),(0,a.jsxs)(l(),{href:"/dashboard",onClick:R,className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target",children:[(0,a.jsx)(h,{className:"h-4 w-4"}),"Dashboard"]}),(0,a.jsxs)("button",{onClick:M,className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target w-full text-left",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),"IDE Preferences"]}),(0,a.jsxs)(l(),{href:"/settings",onClick:R,className:"flex items-center gap-3 py-2 text-sm font-medium transition-colors hover:text-primary touch-target",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"Settings"]}),(0,a.jsxs)("button",{onClick:L,className:"flex items-center gap-3 py-2 text-sm font-medium text-destructive transition-colors hover:text-destructive/80 touch-target w-full text-left",children:[(0,a.jsx)(p,{className:"h-4 w-4"}),"Sign Out"]})]}):(0,a.jsx)("div",{className:"border-t pt-4",children:(0,a.jsx)(l(),{href:"/auth/signin",onClick:R,className:"block w-full text-center py-3 px-4 bg-primary text-primary-foreground rounded-lg font-medium text-sm touch-target",children:"Sign In"})})]})]})})]}),(0,a.jsx)(z,{open:A,onOpenChange:C})]})}function G(e){let{locale:s}=e;return(0,a.jsx)(R,{locale:s})}}},e=>{e.O(0,[1065,795,7261,8560,8916,2953,7610,4826,3096,587,1902,7358],()=>e(e.s=48253)),_N_E=e.O()}]);