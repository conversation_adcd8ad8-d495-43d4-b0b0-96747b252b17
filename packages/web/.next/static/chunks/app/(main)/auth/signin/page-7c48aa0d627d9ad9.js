(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1709],{5007:(e,s,t)=>{"use strict";t.d(s,{b:()=>a});let a={align:{type:"enum",className:"rt-r-ta",values:["left","center","right"],responsive:!0}}},12849:(e,s,t)=>{"use strict";t.d(s,{$:()=>a});let a={trim:{type:"enum",className:"rt-r-lt",values:["normal","start","end","both"],responsive:!0}}},13936:(e,s,t)=>{"use strict";t.d(s,{J:()=>a});let a={truncate:{type:"boolean",className:"rt-truncate"}}},45798:(e,s,t)=>{"use strict";t.d(s,{L:()=>a});let a={weight:{type:"enum",className:"rt-r-weight",values:["light","regular","medium","bold"],responsive:!0}}},51278:(e,s,t)=>{"use strict";t.d(s,{G:()=>a});let a={wrap:{type:"enum",className:"rt-r-tw",values:["wrap","nowrap","pretty","balance"],responsive:!0}}},58030:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h,dynamic:()=>p});var a=t(54568),r=t(7620),l=t(62942),c=t(27261),n=t.n(c),i=t(77749),u=t(90325),o=t(61065),d=t(67642),m=t(48976),x=t(85962);let p="force-dynamic";function h(){let[e,s]=(0,r.useState)(!1),[t,c]=(0,r.useState)("");(0,l.useRouter)();let p=async()=>{s(!0),c("");try{let e=await d.Jv.social({provider:"github",callbackURL:"/dashboard"});e.error&&c(e.error.message||"Failed to sign in with GitHub")}catch(e){c("An unexpected error occurred")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 py-8 xs:py-12 mobile-padding",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6 xs:space-y-8",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center gap-2 mb-6 xs:mb-8 touch-target",children:[(0,a.jsx)(m.A,{className:"h-6 w-6 xs:h-8 xs:w-8 text-primary"}),(0,a.jsx)("span",{className:"font-bold text-xl xs:text-2xl",children:"OnlyRules"})]})}),(0,a.jsxs)(i.Z,{className:"space-y-4 xs:space-y-6 mobile-card",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-xl xs:text-2xl text-center font-semibold",children:"Welcome back"}),(0,a.jsx)("div",{className:"text-center text-muted-foreground text-sm xs:text-base",children:"Sign in with your GitHub account to continue"})]}),(0,a.jsxs)("div",{className:"space-y-4 xs:space-y-6",children:[t&&(0,a.jsx)(u.Root,{color:"red",children:(0,a.jsx)(u.Text,{className:"text-sm",children:t})}),(0,a.jsxs)(o.$,{onClick:p,className:"w-full touch-target",disabled:e,size:"3",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4 xs:h-5 xs:w-5"}),(0,a.jsx)("span",{className:"text-sm xs:text-base",children:e?"Signing in...":"Continue with GitHub"})]}),(0,a.jsx)("div",{className:"text-center text-xs xs:text-sm text-muted-foreground leading-relaxed px-2",children:"By signing in, you agree to our terms of service and privacy policy."})]})]})]})})}},62942:(e,s,t)=>{"use strict";var a=t(42418);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},67642:(e,s,t)=>{"use strict";t.d(s,{CI:()=>l,Jv:()=>a,wV:()=>c});let{signIn:a,signUp:r,signOut:l,useSession:c,getSession:n}=(0,t(57664).MB)({baseURL:"https://onlyrules.codes"})},68082:(e,s,t)=>{"use strict";t.d(s,{E:()=>v});var a=t(7620),r=t(77785),l=t(79649),c=t(55772),n=t(45225),i=t(74531),u=t(33233),o=t(63135),d=t(12849),m=t(5007),x=t(51278),p=t(13936),h=t(45798);let f={as:{type:"enum",values:["span","div","label","p"],default:"span"},...i.f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],responsive:!0},...h.L,...m.b,...d.$,...p.J,...x.G,...u._s,...o.Z},v=a.forwardRef((e,s)=>{let{children:t,className:i,asChild:u,as:o="span",color:d,...m}=(0,c.o)(e,f,n.y);return a.createElement(l.bL,{"data-accent-color":d,...m,ref:s,className:r("rt-Text",i)},u?t:a.createElement(o,null,t))});v.displayName="Text"},72776:(e,s,t)=>{Promise.resolve().then(t.bind(t,58030))},77749:(e,s,t)=>{"use strict";t.d(s,{Z:()=>u});var a=t(7620),r=t(77785),l=t(79649);let c={...t(74531).f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var n=t(55772),i=t(45225);let u=a.forwardRef((e,s)=>{let{asChild:t,className:u,...o}=(0,n.o)(e,c,i.y),d=t?l.bL:"div";return a.createElement(d,{ref:s,...o,className:r("rt-reset","rt-BaseCard","rt-Card",u)})});u.displayName="Card"},85962:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},90325:(e,s,t)=>{"use strict";t.r(s),t.d(s,{Icon:()=>f,Root:()=>h,Text:()=>v});var a=t(7620),r=t(77785),l=t(79649),c=t(68082),n=t(74531),i=t(33233),u=t(63135);let o={...n.f,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["soft","surface","outline"],default:"soft"},...i.un,...u.Z};var d=t(55772),m=t(26819),x=t(45225);let p=a.createContext({}),h=a.forwardRef((e,s)=>{let{size:t=o.size.default}=e,{asChild:c,children:n,className:i,color:u,...m}=(0,d.o)(e,o,x.y),h=c?l.bL:"div";return a.createElement(h,{"data-accent-color":u,...m,className:r("rt-CalloutRoot",i),ref:s},a.createElement(p.Provider,{value:a.useMemo(()=>({size:t}),[t])},n))});h.displayName="Callout.Root";let f=a.forwardRef((e,s)=>{let{className:t,...l}=e;return a.createElement("div",{...l,className:r("rt-CalloutIcon",t),ref:s})});f.displayName="Callout.Icon";let v=a.forwardRef((e,s)=>{let{className:t,...l}=e,{size:n}=a.useContext(p);return a.createElement(c.E,{as:"p",size:(0,m.AY)(n,m.Rw),...l,asChild:!1,ref:s,className:r("rt-CalloutText",t)})});v.displayName="Callout.Text"}},e=>{e.O(0,[1065,7261,2953,587,1902,7358],()=>e(e.s=72776)),_N_E=e.O()}]);