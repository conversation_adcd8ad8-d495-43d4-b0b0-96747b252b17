(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5683],{5007:(e,t,s)=>{"use strict";s.d(t,{b:()=>a});let a={align:{type:"enum",className:"rt-r-ta",values:["left","center","right"],responsive:!0}}},12849:(e,t,s)=>{"use strict";s.d(t,{$:()=>a});let a={trim:{type:"enum",className:"rt-r-lt",values:["normal","start","end","both"],responsive:!0}}},13936:(e,t,s)=>{"use strict";s.d(t,{J:()=>a});let a={truncate:{type:"boolean",className:"rt-truncate"}}},17928:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v,dynamic:()=>h});var a=s(54568),r=s(7620),l=s(62942),c=s(27261),n=s.n(c),i=s(77749),u=s(90325),o=s(61065),d=s(67642),m=s(48976),p=s(85962);let h="force-dynamic";function v(){let[e,t]=(0,r.useState)(!1),[s,c]=(0,r.useState)("");(0,l.useRouter)();let h=async()=>{t(!0),c("");try{let e=await d.Jv.social({provider:"github",callbackURL:"/dashboard"});e.error&&c(e.error.message||"Failed to sign in with GitHub")}catch(e){c("An unexpected error occurred")}finally{t(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center gap-2 mb-8",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-primary"}),(0,a.jsx)("span",{className:"font-bold text-2xl",children:"OnlyRules"})]})}),(0,a.jsxs)(i.Z,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-2xl text-center font-semibold",children:"Create an account"}),(0,a.jsx)("div",{className:"text-center text-muted-foreground",children:"Sign up with your GitHub account to get started"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[s&&(0,a.jsx)(u.Root,{color:"red",children:(0,a.jsx)(u.Text,{children:s})}),(0,a.jsxs)(o.$,{onClick:h,className:"w-full",disabled:e,size:"3",children:[(0,a.jsx)(p.A,{className:"mr-2 h-5 w-5"}),e?"Signing up...":"Continue with GitHub"]}),(0,a.jsx)("div",{className:"text-center text-sm text-muted-foreground",children:"By signing up, you agree to our terms of service and privacy policy."}),(0,a.jsxs)("div",{className:"text-center text-sm",children:["Already have an account?"," ",(0,a.jsx)(n(),{href:"/auth/signin",className:"text-primary hover:underline",children:"Sign in"})]})]})]})]})})}},45798:(e,t,s)=>{"use strict";s.d(t,{L:()=>a});let a={weight:{type:"enum",className:"rt-r-weight",values:["light","regular","medium","bold"],responsive:!0}}},51278:(e,t,s)=>{"use strict";s.d(t,{G:()=>a});let a={wrap:{type:"enum",className:"rt-r-tw",values:["wrap","nowrap","pretty","balance"],responsive:!0}}},62942:(e,t,s)=>{"use strict";var a=s(42418);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},67642:(e,t,s)=>{"use strict";s.d(t,{CI:()=>l,Jv:()=>a,wV:()=>c});let{signIn:a,signUp:r,signOut:l,useSession:c,getSession:n}=(0,s(57664).MB)({baseURL:"https://onlyrules.codes"})},68082:(e,t,s)=>{"use strict";s.d(t,{E:()=>x});var a=s(7620),r=s(77785),l=s(79649),c=s(55772),n=s(45225),i=s(74531),u=s(33233),o=s(63135),d=s(12849),m=s(5007),p=s(51278),h=s(13936),v=s(45798);let f={as:{type:"enum",values:["span","div","label","p"],default:"span"},...i.f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],responsive:!0},...v.L,...m.b,...d.$,...h.J,...p.G,...u._s,...o.Z},x=a.forwardRef((e,t)=>{let{children:s,className:i,asChild:u,as:o="span",color:d,...m}=(0,c.o)(e,f,n.y);return a.createElement(l.bL,{"data-accent-color":d,...m,ref:t,className:r("rt-Text",i)},u?s:a.createElement(o,null,s))});x.displayName="Text"},77749:(e,t,s)=>{"use strict";s.d(t,{Z:()=>u});var a=s(7620),r=s(77785),l=s(79649);let c={...s(74531).f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var n=s(55772),i=s(45225);let u=a.forwardRef((e,t)=>{let{asChild:s,className:u,...o}=(0,n.o)(e,c,i.y),d=s?l.bL:"div";return a.createElement(d,{ref:t,...o,className:r("rt-reset","rt-BaseCard","rt-Card",u)})});u.displayName="Card"},80114:(e,t,s)=>{Promise.resolve().then(s.bind(s,17928))},85962:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(98889).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},90325:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Icon:()=>f,Root:()=>v,Text:()=>x});var a=s(7620),r=s(77785),l=s(79649),c=s(68082),n=s(74531),i=s(33233),u=s(63135);let o={...n.f,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["soft","surface","outline"],default:"soft"},...i.un,...u.Z};var d=s(55772),m=s(26819),p=s(45225);let h=a.createContext({}),v=a.forwardRef((e,t)=>{let{size:s=o.size.default}=e,{asChild:c,children:n,className:i,color:u,...m}=(0,d.o)(e,o,p.y),v=c?l.bL:"div";return a.createElement(v,{"data-accent-color":u,...m,className:r("rt-CalloutRoot",i),ref:t},a.createElement(h.Provider,{value:a.useMemo(()=>({size:s}),[s])},n))});v.displayName="Callout.Root";let f=a.forwardRef((e,t)=>{let{className:s,...l}=e;return a.createElement("div",{...l,className:r("rt-CalloutIcon",s),ref:t})});f.displayName="Callout.Icon";let x=a.forwardRef((e,t)=>{let{className:s,...l}=e,{size:n}=a.useContext(h);return a.createElement(c.E,{as:"p",size:(0,m.AY)(n,m.Rw),...l,asChild:!1,ref:t,className:r("rt-CalloutText",s)})});x.displayName="Callout.Text"}},e=>{e.O(0,[1065,7261,2953,587,1902,7358],()=>e(e.s=80114)),_N_E=e.O()}]);