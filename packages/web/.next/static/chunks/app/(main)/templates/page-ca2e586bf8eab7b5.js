(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6471],{13643:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(98889).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},48976:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(98889).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},78864:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(98889).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},93704:(e,a,s)=>{Promise.resolve().then(s.bind(s,97092))},97092:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>N,dynamic:()=>y});var t=s(54568),r=s(7620),l=s(77749),n=s(61689),i=s(42716),c=s(58983),o=s(19630),d=s(61065),m=s(48976),u=s(13643),h=s(78864),x=s(82055),p=s(15173),g=s(44795),j=s(13185),f=s(32461),v=s(51874);let y="force-dynamic";function N(){let[e,a]=(0,r.useState)([]),[s,y]=(0,r.useState)([]),[N,A]=(0,r.useState)(!0),[w,C]=(0,r.useState)(""),[b,I]=(0,r.useState)([]),[E,L]=(0,r.useState)("ALL"),k=(0,r.useCallback)(async()=>{try{let e=new URLSearchParams;e.set("visibility","PUBLIC"),w&&e.set("search",w),b.length>0&&e.set("tags",b.join(",")),"ALL"!==E&&e.set("ideType",E);let s=await fetch("/api/rules?".concat(e)),t=await s.json();a(Array.isArray(t)?t:[])}catch(e){console.error("Error fetching rules:",e),v.oR.error("Failed to fetch templates"),a([])}finally{A(!1)}},[w,b,E]),T=async()=>{try{let e=await fetch("/api/tags"),a=await e.json();y(Array.isArray(a)?a:[])}catch(e){console.error("Error fetching tags:",e),y([])}};(0,r.useEffect)(()=>{k(),T()},[k,w,b,E]);let O=Array.isArray(e)?e.slice(0,6):[],R=Array.isArray(e)?e:[],U=[{name:"Code Generation",description:"Templates for generating code snippets and functions",icon:m.A,count:R.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("generation"))).length},{name:"Code Review",description:"Templates for automated code review and analysis",icon:u.A,count:R.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("review"))).length},{name:"Optimization",description:"Templates for code optimization and performance",icon:h.A,count:R.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("optimization"))).length},{name:"Documentation",description:"Templates for generating documentation",icon:x.A,count:R.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("documentation"))).length}];return(0,t.jsxs)("div",{className:"container py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-4xl font-bold",children:"Template Library"})]}),(0,t.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Discover and use community-created AI prompt rules to boost your coding productivity"})]}),(0,t.jsxs)("section",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Browse by Category"}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:U.map(e=>(0,t.jsxs)(l.Z,{className:"hover:shadow-lg transition-shadow cursor-pointer",children:[(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,t.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-lg font-semibold",children:e.name}),(0,t.jsxs)(n.E,{variant:"soft",children:[e.count," templates"]})]})]})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})})]},e.name))})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(i.Root,{placeholder:"Search templates...",value:w,onChange:e=>C(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(c.Root,{value:E,onValueChange:L,children:[(0,t.jsx)(c.Trigger,{className:"w-full md:w-48",placeholder:"IDE Type"}),(0,t.jsxs)(c.Content,{children:[(0,t.jsx)(c.Item,{value:"ALL",children:"All IDEs"}),(0,t.jsx)(c.Item,{value:"GENERAL",children:"General"}),(0,t.jsx)(c.Item,{value:"CURSOR",children:"Cursor"}),(0,t.jsx)(c.Item,{value:"AUGMENT",children:"Augment Code"}),(0,t.jsx)(c.Item,{value:"WINDSURF",children:"Windsurf"}),(0,t.jsx)(c.Item,{value:"CLAUDE",children:"Claude"}),(0,t.jsx)(c.Item,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,t.jsx)(c.Item,{value:"GEMINI",children:"Gemini"}),(0,t.jsx)(c.Item,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,t.jsx)(c.Item,{value:"CLINE",children:"Cline"}),(0,t.jsx)(c.Item,{value:"JUNIE",children:"Junie"}),(0,t.jsx)(c.Item,{value:"TRAE",children:"Trae"}),(0,t.jsx)(c.Item,{value:"LINGMA",children:"Lingma"}),(0,t.jsx)(c.Item,{value:"KIRO",children:"Kiro"}),(0,t.jsx)(c.Item,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),s.length>0&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Filter by tags:"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:s.map(e=>(0,t.jsx)(n.E,{variant:b.includes(e.name)?"solid":"outline",className:"cursor-pointer",onClick:()=>{var a;return a=e.name,void I(e=>e.includes(a)?e.filter(e=>e!==a):[...e,a])},style:{borderColor:e.color,backgroundColor:b.includes(e.name)?e.color:"transparent"},children:e.name},e.id))})]}),(0,t.jsxs)(o.bL,{defaultValue:"featured",className:"space-y-6",children:[(0,t.jsxs)(o.B8,{children:[(0,t.jsx)(o.l9,{value:"featured",children:"Featured"}),(0,t.jsxs)(o.l9,{value:"all",children:["All Templates (",R.length,")"]})]}),(0,t.jsx)(o.UC,{value:"featured",className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold",children:"Featured Templates"}),0===O.length?(0,t.jsxs)(l.Z,{className:"py-12 text-center",children:[(0,t.jsx)(j.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No featured templates yet"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Check back later for curated templates from the community"})]}):(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:O.map(e=>(0,t.jsx)(f.z,{rule:e},e.id))})]})}),(0,t.jsx)(o.UC,{value:"all",className:"space-y-6",children:N?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-muted-foreground",children:"Loading templates..."})]}):0===R.length?(0,t.jsxs)(l.Z,{className:"py-12 text-center",children:[(0,t.jsx)(u.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No templates found"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:"Try adjusting your search criteria or check back later"}),(0,t.jsx)(d.$,{variant:"outline",onClick:()=>{C(""),I([]),L("ALL")},children:"Clear Filters"})]}):(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:R.map(e=>(0,t.jsx)(f.z,{rule:e},e.id))})})]})]})}},98889:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});var t=s(7620);let r=function(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return a.filter((e,a,s)=>!!e&&s.indexOf(e)===a).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,a)=>{let{color:s="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:m,...u}=e;return(0,t.createElement)("svg",{ref:a,...l,width:n,height:n,stroke:s,strokeWidth:c?24*Number(i)/Number(n):i,className:r("lucide",o),...u},[...m.map(e=>{let[a,s]=e;return(0,t.createElement)(a,s)}),...Array.isArray(d)?d:[d]])}),i=(e,a)=>{let s=(0,t.forwardRef)((s,l)=>{let{className:i,...c}=s;return(0,t.createElement)(n,{ref:l,iconNode:a,className:r("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),i),...c})});return s.displayName="".concat(e),s}}},e=>{e.O(0,[7401,1065,795,7261,8560,8916,7610,3096,3243,2461,587,1902,7358],()=>e(e.s=93704)),_N_E=e.O()}]);