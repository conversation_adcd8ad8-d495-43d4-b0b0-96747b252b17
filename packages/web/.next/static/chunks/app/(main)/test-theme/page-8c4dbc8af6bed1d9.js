(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7604],{19955:(e,s,i)=>{Promise.resolve().then(i.bind(i,81421))},81421:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>E});var r=i(54568),l=i(34736),n=i(64722),c=i(83876),a=i(60057),d=i(68082),h=i(81357),o=i(77749),t=i(61065),x=i(42716),j=i(58983),m=i(19630),g=i(44651),p=i(61689),u=i(34964),v=i(7620);function E(){let[e,s]=(0,v.useState)(!1);return(0,r.jsx)(l.m,{size:"3",py:"8",children:(0,r.jsxs)(n.s,{direction:"column",gap:"8",children:[(0,r.jsxs)(c.a,{children:[(0,r.jsx)(a.D,{size:"8",mb:"4",children:"Radix UI Theme v3 Test Page"}),(0,r.jsx)(d.E,{size:"4",color:"gray",children:"This page demonstrates the proper implementation of Radix UI Theme v3 components with consistent styling and design patterns."})]}),(0,r.jsxs)(h.x,{columns:{initial:"1",md:"3"},gap:"4",children:[(0,r.jsx)(o.Z,{children:(0,r.jsxs)(n.s,{direction:"column",gap:"3",children:[(0,r.jsx)(a.D,{size:"4",children:"Classic Card"}),(0,r.jsx)(d.E,{color:"gray",children:"This is a classic card with default styling from Radix UI Themes."}),(0,r.jsx)(t.$,{variant:"soft",children:"Learn More"})]})}),(0,r.jsx)(o.Z,{variant:"surface",children:(0,r.jsxs)(n.s,{direction:"column",gap:"3",children:[(0,r.jsx)(a.D,{size:"4",children:"Surface Card"}),(0,r.jsx)(d.E,{color:"gray",children:"Surface variant provides a subtle background color."}),(0,r.jsx)(t.$,{variant:"surface",children:"Explore"})]})}),(0,r.jsx)(o.Z,{variant:"ghost",children:(0,r.jsxs)(n.s,{direction:"column",gap:"3",children:[(0,r.jsx)(a.D,{size:"4",children:"Ghost Card"}),(0,r.jsx)(d.E,{color:"gray",children:"Ghost variant has minimal visual styling."}),(0,r.jsx)(t.$,{variant:"outline",children:"Discover"})]})})]}),(0,r.jsxs)(o.Z,{size:"3",children:[(0,r.jsx)(a.D,{size:"5",mb:"4",children:"Form Elements"}),(0,r.jsxs)(n.s,{direction:"column",gap:"4",children:[(0,r.jsxs)(c.a,{children:[(0,r.jsx)(d.E,{as:"label",size:"2",weight:"medium",htmlFor:"name",children:"Name"}),(0,r.jsx)(x.Root,{id:"name",placeholder:"Enter your name",mt:"1"})]}),(0,r.jsxs)(c.a,{children:[(0,r.jsx)(d.E,{as:"label",size:"2",weight:"medium",htmlFor:"role",children:"Role"}),(0,r.jsxs)(j.Root,{defaultValue:"developer",children:[(0,r.jsx)(j.Trigger,{id:"role",placeholder:"Select a role"}),(0,r.jsxs)(j.Content,{children:[(0,r.jsx)(j.Item,{value:"developer",children:"Developer"}),(0,r.jsx)(j.Item,{value:"designer",children:"Designer"}),(0,r.jsx)(j.Item,{value:"manager",children:"Manager"})]})]})]}),(0,r.jsxs)(n.s,{gap:"3",mt:"4",children:[(0,r.jsx)(t.$,{variant:"solid",children:"Submit"}),(0,r.jsx)(t.$,{variant:"soft",color:"gray",children:"Cancel"})]})]})]}),(0,r.jsx)(o.Z,{children:(0,r.jsxs)(m.bL,{defaultValue:"overview",children:[(0,r.jsxs)(m.B8,{children:[(0,r.jsx)(m.l9,{value:"overview",children:"Overview"}),(0,r.jsx)(m.l9,{value:"details",children:"Details"}),(0,r.jsx)(m.l9,{value:"settings",children:"Settings"})]}),(0,r.jsxs)(c.a,{pt:"3",children:[(0,r.jsx)(m.UC,{value:"overview",children:(0,r.jsx)(d.E,{size:"2",children:"This is the overview tab content. It demonstrates how tabs work in Radix UI Themes with proper spacing and typography."})}),(0,r.jsx)(m.UC,{value:"details",children:(0,r.jsxs)(g.bL,{children:[(0,r.jsxs)(g.q7,{children:[(0,r.jsx)(g.JU,{children:"Status"}),(0,r.jsx)(g.WT,{children:(0,r.jsx)(p.E,{color:"green",children:"Active"})})]}),(0,r.jsxs)(g.q7,{children:[(0,r.jsx)(g.JU,{children:"Version"}),(0,r.jsx)(g.WT,{children:"3.0.0"})]}),(0,r.jsxs)(g.q7,{children:[(0,r.jsx)(g.JU,{children:"Theme"}),(0,r.jsx)(g.WT,{children:"Radix UI"})]})]})}),(0,r.jsx)(m.UC,{value:"settings",children:(0,r.jsx)(d.E,{size:"2",children:"Settings configuration would go here."})})]})]})}),(0,r.jsx)(n.s,{gap:"3",children:(0,r.jsxs)(u.bL,{open:e,onOpenChange:s,children:[(0,r.jsx)(u.l9,{children:(0,r.jsx)(t.$,{children:"Open Dialog"})}),(0,r.jsxs)(u.UC,{maxWidth:"450px",children:[(0,r.jsx)(u.hE,{children:"Example Dialog"}),(0,r.jsx)(u.VY,{size:"2",mb:"4",children:"This dialog demonstrates proper theming with Radix UI v3."}),(0,r.jsxs)(n.s,{direction:"column",gap:"3",children:[(0,r.jsx)(x.Root,{placeholder:"Enter some text..."}),(0,r.jsx)(d.E,{size:"2",color:"gray",children:"Dialog content is properly styled and accessible."})]}),(0,r.jsxs)(n.s,{gap:"3",mt:"4",justify:"end",children:[(0,r.jsx)(u.bm,{children:(0,r.jsx)(t.$,{variant:"soft",color:"gray",children:"Cancel"})}),(0,r.jsx)(t.$,{children:"Save Changes"})]})]})]})}),(0,r.jsxs)(c.a,{children:[(0,r.jsx)(a.D,{size:"5",mb:"4",children:"Color System"}),(0,r.jsxs)(n.s,{gap:"2",wrap:"wrap",children:[(0,r.jsx)(p.E,{children:"Default"}),(0,r.jsx)(p.E,{color:"blue",children:"Blue"}),(0,r.jsx)(p.E,{color:"green",children:"Green"}),(0,r.jsx)(p.E,{color:"red",children:"Red"}),(0,r.jsx)(p.E,{color:"orange",children:"Orange"}),(0,r.jsx)(p.E,{color:"purple",children:"Purple"}),(0,r.jsx)(p.E,{color:"pink",children:"Pink"}),(0,r.jsx)(p.E,{color:"yellow",children:"Yellow"})]})]})]})})}}},e=>{e.O(0,[1065,795,8560,6452,587,1902,7358],()=>e(e.s=19955)),_N_E=e.O()}]);