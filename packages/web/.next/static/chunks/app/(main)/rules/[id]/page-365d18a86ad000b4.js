(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4255],{13010:(e,s,t)=>{Promise.resolve().then(t.bind(t,73240))},15685:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},20232:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},24772:(e,s,t)=>{"use strict";t.d(s,{l:()=>i,q:()=>l});var a=t(78021),r=t(51874);let l=new a.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,s)=>(!((null==s?void 0:s.status)>=400)||!((null==s?void 0:s.status)<500))&&e<3,refetchOnWindowFocus:!1,throwOnError:!1},mutations:{retry:!1,onError:e=>{console.error("Mutation error:",e);let s=(null==e?void 0:e.message)||"An error occurred";r.oR.error(s)}}}}),i={rules:{all:["rules"],lists:()=>[...i.rules.all,"list"],list:e=>[...i.rules.lists(),e],details:()=>[...i.rules.all,"detail"],detail:e=>[...i.rules.details(),e],raw:e=>[...i.rules.all,"raw",e]},rulesets:{all:["rulesets"],lists:()=>[...i.rulesets.all,"list"],list:e=>[...i.rulesets.lists(),e],details:()=>[...i.rulesets.all,"detail"],detail:e=>[...i.rulesets.details(),e]},tags:{all:["tags"],lists:()=>[...i.tags.all,"list"]},user:{all:["user"],profile:e=>[...i.user.all,"profile",e]}}},58124:(e,s,t)=>{"use strict";t.d(s,{El:()=>d,fs:()=>o,qT:()=>c});var a=t(73297),r=t(87606),l=t(24772);t(51874);let i=async e=>{let s=await fetch("/api/rules/".concat(e));if(!s.ok)throw{message:404===s.status?"Rule not found":403===s.status?"You don't have permission to view this rule":"Failed to load rule",status:s.status};return s.json()},n=async function(){var e;let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;s.search&&t.set("search",s.search),(null==(e=s.tags)?void 0:e.length)&&t.set("tags",s.tags.join(",")),s.ideType&&t.set("ideType",s.ideType),s.visibility&&t.set("visibility",s.visibility),s.page&&t.set("page",s.page.toString()),s.limit&&t.set("limit",s.limit.toString());let a=await fetch("/api/rules?".concat(t));if(!a.ok)throw Error("Failed to fetch rules");let r=await a.json();return{rules:Array.isArray(r)?r:r.rules||[],totalCount:r.totalCount||r.length||0,totalPages:r.totalPages||1,currentPage:r.currentPage||1}},c=e=>(0,a.I)({queryKey:l.l.rules.detail(e),queryFn:()=>i(e),enabled:!!e,staleTime:3e5,retry:(e,s)=>(null==s?void 0:s.status)!==404&&(null==s?void 0:s.status)!==403&&e<3}),o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,a.I)({queryKey:l.l.rules.list(e),queryFn:()=>n(e),staleTime:12e4,keepPreviousData:!0})},d=()=>{let e=(0,r.jE)();return()=>{e.invalidateQueries({queryKey:l.l.rules.all})}}},62942:(e,s,t)=>{"use strict";var a=t(42418);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},73240:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f,dynamic:()=>y});var a=t(54568),r=t(62942),l=t(61065),i=t(77749),n=t(61689);let c=(0,t(98889).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var o=t(76608),d=t(20232),u=t(93385),m=t(15685),h=t(51874),x=t(27261),p=t.n(x),v=t(58124);let y="force-dynamic";function f(){let e=(0,r.useParams)().id,{data:s,isLoading:t,error:x,isError:y}=(0,v.qT)(e),f=async()=>{if(s)try{await navigator.clipboard.writeText(s.content),h.oR.success("Rule content copied to clipboard")}catch(e){h.oR.error("Failed to copy content")}},g=async()=>{try{let s="".concat(window.location.origin,"/r/").concat(e);await navigator.clipboard.writeText(s),h.oR.success("Rule link copied to clipboard")}catch(e){h.oR.error("Failed to copy link")}};if(t)return(0,a.jsx)("div",{className:"container max-w-4xl py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Loading Rule..."}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Rule ID: ",e]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto"})})})]})});if(y||!s){let e=x instanceof Error?x.message:"Rule not found";return(0,a.jsx)("div",{className:"container max-w-4xl py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4 text-red-600",children:"Error"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:e}),(0,a.jsx)(l.$,{variant:"outline",asChild:!0,children:(0,a.jsxs)(p(),{href:"/rules",children:[(0,a.jsx)(c,{className:"mr-2 h-4 w-4"}),"Back to Rules"]})})]})})}return(0,a.jsxs)("div",{className:"container max-w-4xl py-8 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(l.$,{variant:"ghost",size:"1",asChild:!0,children:(0,a.jsx)(p(),{href:"/rules",children:(0,a.jsx)(c,{className:"h-4 w-4"})})}),(0,a.jsx)("span",{children:"Rules"})]}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:s.title}),s.description&&(0,a.jsx)("p",{className:"text-lg text-muted-foreground",children:s.description})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"2",onClick:f,children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Copy"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"2",onClick:g,children:[(0,a.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Share"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"2",onClick:()=>{if(!s)return;let e=new Blob([s.content],{type:"text/plain"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="".concat(s.title.replace(/[^a-z0-9]/gi,"_").toLowerCase(),".md"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(t),h.oR.success("Rule downloaded")},children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Download"]})]})]}),(0,a.jsxs)(i.Z,{className:"p-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"IDE:"}),(0,a.jsx)(n.E,{variant:"outline",children:s.ideType})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Visibility:"}),(0,a.jsx)(n.E,{variant:"PUBLIC"===s.visibility?"solid":"outline",children:s.visibility})]}),s.user&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Author:"}),(0,a.jsx)("span",{className:"text-sm",children:s.user.name||s.user.email})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Created:"}),(0,a.jsx)("span",{className:"text-sm",children:new Date(s.createdAt).toLocaleDateString()})]})]}),s.tags&&s.tags.length>0&&(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Tags:"}),s.tags.map(e=>(0,a.jsx)(n.E,{variant:"soft",style:{backgroundColor:e.tag.color+"20",color:e.tag.color},children:e.tag.name},e.tag.id))]})})]}),(0,a.jsx)(i.Z,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Rule Content"}),(0,a.jsx)(l.$,{variant:"ghost",size:"1",onClick:f,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm font-mono overflow-x-auto",children:s.content})})]})}),"PUBLIC"===s.visibility&&(0,a.jsx)(i.Z,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Raw Data Access"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Access the raw rule content directly via API"})]}),(0,a.jsx)(l.$,{variant:"outline",size:"2",asChild:!0,children:(0,a.jsxs)(p(),{href:"/api/rules/raw?id=".concat(s.id),target:"_blank",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Raw Data"]})})]})})]})}},76608:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},77749:(e,s,t)=>{"use strict";t.d(s,{Z:()=>o});var a=t(7620),r=t(77785),l=t(79649);let i={...t(74531).f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var n=t(55772),c=t(45225);let o=a.forwardRef((e,s)=>{let{asChild:t,className:o,...d}=(0,n.o)(e,i,c.y),u=t?l.bL:"div";return a.createElement(u,{ref:s,...d,className:r("rt-reset","rt-BaseCard","rt-Card",o)})});o.displayName="Card"},93385:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(98889).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},98889:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(7620);let r=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&t.indexOf(e)===s).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:i=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:s,...l,width:i,height:i,stroke:t,strokeWidth:c?24*Number(n)/Number(i):n,className:r("lucide",o),...m},[...u.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(d)?d:[d]])}),n=(e,s)=>{let t=(0,a.forwardRef)((t,l)=>{let{className:n,...c}=t;return(0,a.createElement)(i,{ref:l,iconNode:s,className:r("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),n),...c})});return t.displayName="".concat(e),t}}},e=>{e.O(0,[1065,7261,7610,4826,3297,587,1902,7358],()=>e(e.s=13010)),_N_E=e.O()}]);