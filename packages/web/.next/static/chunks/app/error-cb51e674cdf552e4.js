(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8039],{51480:(e,n,r)=>{"use strict";r.r(n),r.d(n,{default:()=>i});var t=r(54568);function i(e){let{reset:n}=e;return(0,t.jsx)("html",{children:(0,t.jsx)("body",{children:(0,t.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",fontFamily:"system-ui, sans-serif",padding:"20px"},children:[(0,t.jsx)("h1",{style:{fontSize:"2rem",marginBottom:"1rem"},children:"Something went wrong!"}),(0,t.jsx)("p",{style:{marginBottom:"2rem",textAlign:"center"},children:"We encountered an unexpected error. Please try again."}),(0,t.jsx)("button",{onClick:n,style:{padding:"12px 24px",backgroundColor:"#0070f3",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"1rem"},children:"Try again"})]})})})}},80016:(e,n,r)=>{Promise.resolve().then(r.bind(r,51480))}},e=>{e.O(0,[587,1902,7358],()=>e(e.s=80016)),_N_E=e.O()}]);