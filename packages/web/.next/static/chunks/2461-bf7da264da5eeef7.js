"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2461],{7321:(e,s,a)=>{a.d(s,{VG:()=>i,in:()=>r});var t=a(79924),n=a(25474);let i={CURSOR:{name:"<PERSON>urs<PERSON>",description:"AI-powered code editor",command:"cursor"},AUGMENT:{name:"Augment",description:"AI coding assistant",command:"augment"},WINDSURF:{name:"Windsurf",description:"AI development environment",command:"windsurf"},CLAUDE:{name:"<PERSON>",description:"Anthropic AI assistant",command:"claude"},GITHUB_COPILOT:{name:"GitHub Copilot",description:"AI pair programmer",command:"github-copilot"},GEMINI:{name:"Gemini",description:"Google AI assistant",command:"gemini"},OPENAI_CODEX:{name:"OpenAI Codex",description:"OpenAI code assistant",command:"openai-codex"},CLINE:{name:"<PERSON><PERSON>",description:"AI coding assistant",command:"cline"},JUNIE:{name:"<PERSON>ie",description:"AI development tool",command:"junie"},TRAE:{name:"Trae",description:"AI coding companion",command:"trae"},LINGMA:{name:"Lingma",description:"AI programming assistant",command:"lingma"},KIRO:{name:"Kiro",description:"AI development environment",command:"kiro"},TENCENT_CODEBUDDY:{name:"Tencent CodeBuddy",description:"Tencent AI coding assistant",command:"tencent-codebuddy"},GENERAL:{name:"General",description:"General purpose IDE",command:"general"}};(0,t.eU)([]),(0,t.eU)([]),(0,t.eU)(null),(0,t.eU)(""),(0,t.eU)([]),(0,t.eU)("ALL"),(0,n.tG)("theme","system");let r=(0,n.tG)("ide-preferences",{preferredIDEs:[],defaultIDE:void 0});(0,t.eU)(e=>e(r).preferredIDEs,(e,s,a)=>{let t=e(r);s(r,{...t,preferredIDEs:a})}),(0,t.eU)(e=>{let s=e(r);return s.preferredIDEs.find(e=>e.id===s.defaultIDE)},(e,s,a)=>{let t=e(r);s(r,{...t,defaultIDE:a})})},32461:(e,s,a)=>{a.d(s,{z:()=>O});var t=a(54568),n=a(7620),i=a(60844),r=a(77749),c=a(69244),o=a(61065),l=a(61689),d=a(34964),m=a(15685),p=a(71690),x=a(85958),u=a(76608),h=a(66481),g=a(13185),f=a(48976),j=a(20232),w=a(93385),b=a(37160),N=a(93425),I=a(59388),y=a(73385),v=a(7321);function C(e,s){let a=v.VG[s].command;return'npx onlyrules -f "'.concat(e,'" --target ').concat(a)}function E(e){return'npx onlyrules -f "'.concat(e,'"')}function D(e,s){return[...e].sort((e,a)=>e.id===s?-1:a.id===s?1:e.name.localeCompare(a.name))}async function A(e){try{return await navigator.clipboard.writeText(e),!0}catch(e){return console.error("Failed to copy to clipboard:",e),!1}}var k=a(76945),R=a(51874),U=a(27261),L=a.n(U);function O(e){var s,a,U;let{rule:O,onEdit:T,onDelete:S,isOwner:B=!1}=e,[G,P]=(0,n.useState)(!1),[M,F]=(0,n.useState)(!1),[_]=(0,i.fp)(v.in),z=async()=>{await navigator.clipboard.writeText(O.content),R.oR.success("Rule content copied to clipboard")},J=async()=>{if("PUBLIC"!==O.visibility)return void R.oR.error("Only public rules can be used with npx command");let e=E("".concat(window.location.origin,"/api/rules/raw?id=").concat(O.id));await A(e)?(F(!0),R.oR.success("CLI command copied to clipboard"),setTimeout(()=>F(!1),2e3)):R.oR.error("Failed to copy command to clipboard")},X=async e=>{if("PUBLIC"!==O.visibility)return void R.oR.error("Only public rules can be used with npx command");let s=_.preferredIDEs.find(s=>s.id===e);if(!s)return;let a=C("".concat(window.location.origin,"/api/rules/raw?id=").concat(O.id),s.type);await A(a)?R.oR.success("".concat(s.name," command copied to clipboard")):R.oR.error("Failed to copy command to clipboard")},$=async()=>{if("PUBLIC"!==O.visibility)return void R.oR.error("Only public rules can be used with npx command");let e=_.preferredIDEs.find(e=>e.id===_.defaultIDE);if(!e)return J();let s=C("".concat(window.location.origin,"/api/rules/raw?id=").concat(O.id),e.type);await A(s)?(F(!0),R.oR.success("".concat(e.name," command copied to clipboard")),setTimeout(()=>F(!1),2e3)):R.oR.error("Failed to copy command to clipboard")},H=async()=>{if("PUBLIC"===O.visibility){let e="".concat(window.location.origin,"/rules/").concat(O.id);await navigator.clipboard.writeText(e),R.oR.success("Share link copied to clipboard")}else if(O.shareToken){let e="".concat(window.location.origin,"/shared/").concat(O.shareToken);await navigator.clipboard.writeText(e),R.oR.success("Share link copied to clipboard")}},V=async()=>{if("PUBLIC"!==O.visibility)return void R.oR.error("Only public rules can be downloaded as MDX");try{let e=await fetch("/api/rules/download?id=".concat(O.id));if(!e.ok)throw Error("Failed to download rule");let s=await e.blob(),a=URL.createObjectURL(s),t=document.createElement("a");t.href=a,t.download="".concat(O.title.toLowerCase().replace(/\s+/g,"-"),".mdx"),document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(a),R.oR.success("Rule downloaded as MDX")}catch(e){R.oR.error("Failed to download rule as MDX")}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r.Z,{className:"group hover:shadow-md transition-all duration-200 mobile-card",children:[(0,t.jsx)("div",{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsxs)("div",{className:"space-y-2 flex-1 min-w-0",children:["PUBLIC"===O.visibility?(0,t.jsx)(L(),{href:"/rules/".concat(O.id),className:"touch-target",children:(0,t.jsxs)("div",{className:"text-base xs:text-lg font-semibold cursor-pointer hover:text-primary transition-colors inline-flex items-center gap-1",children:[(0,t.jsx)("span",{className:"truncate",children:O.title}),(0,t.jsx)(m.A,{className:"h-3 w-3 opacity-0 group-hover:opacity-50 flex-shrink-0"})]})}):(0,t.jsx)("div",{className:"text-base xs:text-lg font-semibold cursor-pointer hover:text-primary transition-colors touch-target truncate",onClick:()=>P(!0),children:O.title}),O.description&&(0,t.jsx)("p",{className:"text-xs xs:text-sm text-muted-foreground line-clamp-2 leading-relaxed",children:O.description})]}),(0,t.jsxs)(c.Root,{children:[(0,t.jsx)(c.Trigger,{children:(0,t.jsx)(o.$,{variant:"ghost",size:"1",className:"opacity-60 xs:opacity-0 xs:group-hover:opacity-100 transition-opacity touch-target flex-shrink-0",children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(c.Content,{align:"end",children:[(0,t.jsxs)(c.Item,{onClick:()=>P(!0),children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"View"]}),"PUBLIC"===O.visibility&&(0,t.jsx)(c.Item,{asChild:!0,children:(0,t.jsxs)(L(),{href:"/rules/".concat(O.id),children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Open Rule Page"]})}),(0,t.jsxs)(c.Item,{onClick:z,children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Copy Content"]}),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c.Sub,{children:[(0,t.jsxs)(c.SubTrigger,{children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Copy CLI Command"]}),(0,t.jsxs)(c.SubContent,{children:[_.defaultIDE&&_.preferredIDEs.length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(c.Item,{onClick:$,children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4 fill-current"}),null==(s=_.preferredIDEs.find(e=>e.id===_.defaultIDE))?void 0:s.name," (Default)"]}),(0,t.jsx)(c.Separator,{})]}),(0,t.jsxs)(c.Item,{onClick:J,children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Basic Command"]}),_.preferredIDEs.length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.Separator,{}),D(_.preferredIDEs,_.defaultIDE).filter(e=>e.id!==_.defaultIDE).map(e=>(0,t.jsxs)(c.Item,{onClick:()=>X(e.id),children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4"}),e.name]},e.id))]}),0===_.preferredIDEs.length&&(0,t.jsx)("div",{className:"px-2 py-1.5 text-xs text-muted-foreground",children:"Add IDE preferences in settings for quick commands"})]})]}),(0,t.jsxs)(c.Item,{onClick:H,children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Share"]})]}),(0,t.jsxs)(c.Sub,{children:[(0,t.jsxs)(c.SubTrigger,{children:[(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Download"]}),(0,t.jsxs)(c.SubContent,{children:[(0,t.jsxs)(c.Item,{onClick:()=>{let e=new Blob([JSON.stringify({title:O.title,description:O.description,content:O.content,ideType:O.ideType,tags:O.tags.map(e=>e.tag.name)},null,2)],{type:"application/json"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="".concat(O.title.toLowerCase().replace(/\s+/g,"-"),".json"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s),R.oR.success("Rule downloaded as JSON")},children:[(0,t.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"JSON"]}),(0,t.jsxs)(c.Item,{onClick:V,children:[(0,t.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"MDX"]})]})]}),B&&T&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.Separator,{}),(0,t.jsxs)(c.Item,{onClick:()=>T(O),children:[(0,t.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"Edit"]})]}),B&&S&&(0,t.jsxs)(c.Item,{onClick:()=>S(O.id),className:"text-destructive",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]})}),(0,t.jsxs)("div",{className:"space-y-3 xs:space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,t.jsxs)(l.E,{variant:"soft",className:"".concat((e=>{switch(e){case"CURSOR":return"bg-blue-500";case"AUGMENT":return"bg-green-500";case"WINDSURF":return"bg-purple-500";case"CLAUDE":return"bg-orange-500";case"GITHUB_COPILOT":return"bg-gray-800";case"GEMINI":return"bg-indigo-500";case"OPENAI_CODEX":return"bg-teal-500";case"CLINE":return"bg-pink-500";case"JUNIE":return"bg-yellow-500";case"TRAE":return"bg-red-500";case"LINGMA":return"bg-cyan-500";case"KIRO":return"bg-emerald-500";case"TENCENT_CODEBUDDY":return"bg-violet-500";default:return"bg-gray-500"}})(O.ideType)," text-white text-xs xs:text-sm"),children:[(0,t.jsx)(f.A,{className:"mr-1 h-3 w-3"}),O.ideType]}),"PUBLIC"===O.visibility&&(0,t.jsx)(l.E,{variant:"outline",className:"text-xs xs:text-sm",children:"Public"})]}),O.tags.length>0&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-1 xs:gap-2",children:[O.tags.slice(0,3).map(e=>(0,t.jsx)(l.E,{variant:"outline",style:{borderColor:e.tag.color},className:"text-xs px-2 py-1",children:e.tag.name},e.tag.id)),O.tags.length>3&&(0,t.jsxs)(l.E,{variant:"outline",className:"text-xs px-2 py-1 text-muted-foreground",children:["+",O.tags.length-3," more"]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated ",new Date(O.updatedAt).toLocaleDateString()]})]})]}),(0,t.jsx)(d.bL,{open:G,onOpenChange:P,children:(0,t.jsxs)(d.UC,{className:"max-w-4xl max-h-[90vh] w-[95vw] xs:w-[90vw] overflow-hidden",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.hE,{className:"text-lg xs:text-xl truncate",children:O.title}),O.description&&(0,t.jsx)(d.VY,{className:"text-sm xs:text-base",children:O.description})]}),(0,t.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,t.jsx)(k.B,{value:O.content,onChange:()=>{},className:"h-[50vh] xs:h-[60vh]"})}),"PUBLIC"===O.visibility&&(0,t.jsxs)("div",{className:"mt-4 space-y-3",children:[_.defaultIDE&&_.preferredIDEs.length>0&&(0,t.jsx)("div",{className:"p-3 bg-primary/5 border border-primary/20 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("p",{className:"text-sm font-medium flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 fill-current text-primary"}),null==(a=_.preferredIDEs.find(e=>e.id===_.defaultIDE))?void 0:a.name," (Default):"]}),(0,t.jsx)("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:C("".concat(window.location.origin,"/api/rules/raw?id=").concat(O.id),(null==(U=_.preferredIDEs.find(e=>e.id===_.defaultIDE))?void 0:U.type)||"GENERAL")})]}),(0,t.jsxs)(o.$,{size:"1",variant:"outline",onClick:$,className:"ml-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Copy"]})]})}),(0,t.jsx)("div",{className:"p-3 bg-muted rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Basic CLI Command:"}),(0,t.jsx)("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:E("".concat(window.location.origin,"/api/rules/raw?id=").concat(O.id))})]}),(0,t.jsxs)(o.$,{size:"1",variant:"outline",onClick:J,className:"ml-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),M?"Copied!":"Copy"]})]})}),_.preferredIDEs.length>0&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Other IDE Commands:"}),(0,t.jsx)("div",{className:"grid gap-2",children:D(_.preferredIDEs,_.defaultIDE).filter(e=>e.id!==_.defaultIDE).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted/50 rounded text-xs",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 flex-shrink-0"}),(0,t.jsxs)("span",{className:"font-medium flex-shrink-0",children:[e.name,":"]}),(0,t.jsx)("code",{className:"bg-background px-1 py-0.5 rounded truncate",children:C("".concat(window.location.origin,"/api/rules/raw?id=").concat(O.id),e.type)})]}),(0,t.jsx)(o.$,{size:"1",variant:"ghost",onClick:()=>X(e.id),className:"ml-2 h-6 w-6 p-0",children:(0,t.jsx)(u.A,{className:"h-3 w-3"})})]},e.id))})]})]})]})})]})}},76945:(e,s,a)=>{a.d(s,{B:()=>x});var t=a(54568),n=a(68309),i=a(81353),r=a(72312),c=a(23401),o=a(60901),l=a(5173),d=a(3954),m=a(71824),p=a(14622);function x(e){let{value:s,onChange:a,placeholder:x,className:u,language:h="javascript",readOnly:g=!1}=e,{theme:f}=(0,n.D)(),j=[(()=>{switch(h){case"markdown":return(0,c.wD)();case"json":return(0,o.Pq)();case"xml":return(0,l._n)();default:return(0,r.Q2)()}})(),m.Lz.theme({"&":{fontSize:"14px"},".cm-content":{padding:"16px",minHeight:"200px"},".cm-focused":{outline:"none"},".cm-editor":{borderRadius:"8px"},"&.cm-editor.cm-readonly .cm-content":{backgroundColor:g?"dark"===f?"#1a1a1a":"#f8f9fa":"inherit"}})];return g&&j.push(p.$t.readOnly.of(!0)),(0,t.jsx)("div",{className:u,children:(0,t.jsx)(i.Ay,{value:s,onChange:a?e=>a(e):void 0,placeholder:x,theme:"dark"===f?d.bM:void 0,extensions:j,readOnly:g,basicSetup:{lineNumbers:!0,foldGutter:!0,dropCursor:!1,allowMultipleSelections:!1,indentOnInput:!g,bracketMatching:!0,closeBrackets:!g,autocompletion:!g,highlightSelectionMatches:!1}})})}}}]);