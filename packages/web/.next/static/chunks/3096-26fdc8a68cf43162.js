"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3096],{13185:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(98889).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},25474:(e,t,r)=>{r.d(t,{tG:()=>o});var n=r(79924);let l=Symbol("RESET"),a=e=>"function"==typeof(null==e?void 0:e.then),i=function(e=()=>{try{return window.localStorage}catch(e){"undefined"!=typeof window&&console.warn(e);return}},t){var r;let n,l,i,o,d={getItem:(t,r)=>{var i,o;let d=e=>{if(n!==(e=e||"")){try{l=JSON.parse(e,void 0)}catch(e){return r}n=e}return l},u=null!=(o=null==(i=e())?void 0:i.getItem(t))?o:null;return a(u)?u.then(d):d(u)},setItem:(t,r)=>{var n;return null==(n=e())?void 0:n.setItem(t,JSON.stringify(r,void 0))},removeItem:t=>{var r;return null==(r=e())?void 0:r.removeItem(t)}};try{i=null==(r=e())?void 0:r.subscribe}catch(e){}return!i&&"undefined"!=typeof window&&"function"==typeof window.addEventListener&&window.Storage&&(i=(t,r)=>{if(!(e()instanceof window.Storage))return()=>{};let n=n=>{n.storageArea===e()&&n.key===t&&r(n.newValue)};return window.addEventListener("storage",n),()=>{window.removeEventListener("storage",n)}}),i&&(o=i,d.subscribe=(e,t,r)=>o(e,e=>{let n;try{n=JSON.parse(e||"")}catch(e){n=r}t(n)})),d}();function o(e,t,r=i,d){let u=null==d?void 0:d.getOnInit,s=(0,n.eU)(u?r.getItem(e,t):t);return s.debugPrivate=!0,s.onMount=n=>{let l;return n(r.getItem(e,t)),r.subscribe&&(l=r.subscribe(e,n,t)),l},(0,n.eU)(e=>e(s),(n,i,o)=>{let d="function"==typeof o?o(n(s)):o;return d===l?(i(s,t),r.removeItem(e)):a(d)?d.then(t=>(i(s,t),r.setItem(e,t))):(i(s,d),r.setItem(e,d))})}},34964:(e,t,r)=>{r.d(t,{bm:()=>E,UC:()=>w,VY:()=>p,bL:()=>v,hE:()=>y,l9:()=>m});var n=r(7620),l=r(77785),a=r(25173),i=r(74531),o=r(68405),d=r(41084);let u={...i.f,align:{type:"enum",className:"rt-r-align",values:["start","center"],default:"center"},size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"3",responsive:!0},width:o.w.width,minWidth:o.w.minWidth,maxWidth:{...o.w.maxWidth,default:"600px"},...d.B};var s=r(60057),f=r(68082),c=r(37834),h=r(55772),g=r(30834);let v=e=>n.createElement(a.Root,{...e,modal:!0});v.displayName="Dialog.Root";let m=n.forwardRef(({children:e,...t},r)=>n.createElement(a.Trigger,{...t,ref:r,asChild:!0},(0,g.v)(e)));m.displayName="Dialog.Trigger";let w=n.forwardRef(({align:e,...t},r)=>{let{align:i,...o}=u,{className:d}=(0,h.o)({align:e},{align:i}),{className:s,forceMount:f,container:g,...v}=(0,h.o)(t,o);return n.createElement(a.Portal,{container:g,forceMount:f},n.createElement(c.Theme,{asChild:!0},n.createElement(a.Overlay,{className:"rt-BaseDialogOverlay rt-DialogOverlay"},n.createElement("div",{className:"rt-BaseDialogScroll rt-DialogScroll"},n.createElement("div",{className:`rt-BaseDialogScrollPadding rt-DialogScrollPadding ${d}`},n.createElement(a.Content,{...v,ref:r,className:l("rt-BaseDialogContent","rt-DialogContent",s)}))))))});w.displayName="Dialog.Content";let y=n.forwardRef((e,t)=>n.createElement(a.Title,{asChild:!0},n.createElement(s.D,{size:"5",mb:"3",trim:"start",...e,asChild:!1,ref:t})));y.displayName="Dialog.Title";let p=n.forwardRef((e,t)=>n.createElement(a.Description,{asChild:!0},n.createElement(f.E,{as:"p",size:"3",...e,asChild:!1,ref:t})));p.displayName="Dialog.Description";let E=n.forwardRef(({children:e,...t},r)=>n.createElement(a.Close,{...t,ref:r,asChild:!0},(0,g.v)(e)));E.displayName="Dialog.Close"},60844:(e,t,r)=>{r.d(t,{Kq:()=>d,fp:()=>g});var n=r(7620),l=r(79924),a=r(93827);let i=(0,n.createContext)(void 0);function o(e){let t=(0,n.useContext)(i);return(null==e?void 0:e.store)||t||(0,l.zp)()}function d(e){let{children:t,store:r}=e,a=(0,n.useRef)(void 0);return r||a.current||(a.current=(0,l.y$)()),(0,n.createElement)(i.Provider,{value:r||a.current},t)}let u=e=>"function"==typeof(null==e?void 0:e.then),s=e=>{e.status||(e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}))},f=n.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw s(e),e}),c=new WeakMap,h=(e,t)=>{let r=c.get(e);return r||(r=new Promise((n,l)=>{let i=e,o=e=>t=>{i===e&&n(t)},d=e=>t=>{i===e&&l(t)},s=()=>{try{let e=t();u(e)?(c.set(e,r),i=e,e.then(o(e),d(e)),(0,a.MO)(e,s)):n(e)}catch(e){l(e)}};e.then(o(e),d(e)),(0,a.MO)(e,s)}),c.set(e,r)),r};function g(e,t){return[function(e,t){let{delay:r,unstable_promiseStatus:l=!n.use}=t||{},a=o(t),[[i,d,c],g]=(0,n.useReducer)(t=>{let r=a.get(e);return Object.is(t[0],r)&&t[1]===a&&t[2]===e?t:[r,a,e]},void 0,()=>[a.get(e),a,e]),v=i;if((d!==a||c!==e)&&(g(),v=a.get(e)),(0,n.useEffect)(()=>{let t=a.sub(e,()=>{if(l)try{let t=a.get(e);u(t)&&s(h(t,()=>a.get(e)))}catch(e){}if("number"==typeof r)return void setTimeout(g,r);g()});return g(),t},[a,e,r,l]),(0,n.useDebugValue)(v),u(v)){let t=h(v,()=>a.get(e));return l&&s(t),f(t)}return v}(e,t),function(e,t){let r=o(t);return(0,n.useCallback)(function(){for(var t=arguments.length,n=Array(t),l=0;l<t;l++)n[l]=arguments[l];if(!("write"in e))throw Error("not writable atom");return r.set(e,...n)},[r,e])}(e,t)]}},79924:(e,t,r)=>{let n,l;r.d(t,{eU:()=>o,y$:()=>s,zp:()=>f});var a=r(93827);let i=0;function o(e,t){let r=`atom${++i}`,n={toString(){return this.debugLabel?r+":"+this.debugLabel:r}};return"function"==typeof e?n.read=e:(n.init=e,n.read=d,n.write=u),t&&(n.write=t),n}function d(e){return e(this)}function u(e,t,r){return t(this,"function"==typeof r?r(e(this)):r)}function s(){return n?n():(()=>{let e=0,t=(0,a.eE)({}),r=new WeakMap,n=new WeakMap,l=(0,a._w)(r,n,void 0,void 0,void 0,void 0,t,void 0,(t,r,n,...l)=>e?n(t,...l):t.write(r,n,...l)),i=new Set;return t.m.add(void 0,e=>{i.add(e),r.get(e).m=n.get(e)}),t.u.add(void 0,e=>{i.delete(e);let t=r.get(e);delete t.m}),Object.assign(l,{dev4_get_internal_weak_map:()=>(console.log("Deprecated: Use devstore from the devtools library"),r),dev4_get_mounted_atoms:()=>i,dev4_restore_atoms:t=>{l.set({read:()=>null,write:(r,n)=>{++e;try{for(let[e,r]of t)"init"in e&&n(e,r)}finally{--e}}})}})})()}function f(){return l||(l=s(),globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=l),globalThis.__JOTAI_DEFAULT_STORE__!==l&&console.warn("Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044")),l}},93827:(e,t,r)=>{r.d(t,{MO:()=>w,_w:()=>v,eE:()=>m});let n=(e,t)=>e.unstable_is?e.unstable_is(t):t===e,l=e=>"v"in e||"e"in e,a=e=>{if("e"in e)throw e.e;if(!("v"in e))throw Error("[Bug] atom state is not initialized");return e.v},i=new WeakMap,o=e=>{var t;return u(e)&&!!(null==(t=i.get(e))?void 0:t[0])},d=(e,t)=>{let r=i.get(e);if(!r){r=[!0,new Set],i.set(e,r);let t=()=>{r[0]=!1};e.then(t,t)}r[1].add(t)},u=e=>"function"==typeof(null==e?void 0:e.then),s=(e,t,r)=>{r.p.has(e)||(r.p.add(e),t.then(()=>{r.p.delete(e)},()=>{r.p.delete(e)}))},f=(e,t,r)=>{let n=r(e),l="v"in n,a=n.v;if(u(t))for(let l of n.d.keys())s(e,t,r(l));n.v=t,delete n.e,l&&Object.is(a,n.v)||(++n.n,u(a)&&(e=>{let t=i.get(e);(null==t?void 0:t[0])&&(t[0]=!1,t[1].forEach(e=>e()))})(a))},c=(e,t,r)=>{var n;let l=new Set;for(let t of(null==(n=r.get(e))?void 0:n.t)||[])r.has(t)&&l.add(t);for(let e of t.p)l.add(e);return l},h=()=>{let e={},t=new WeakMap,r=r=>{var n,l;null==(n=t.get(e))||n.forEach(e=>e(r)),null==(l=t.get(r))||l.forEach(e=>e())};return r.add=(r,n)=>{let l=r||e,a=(t.has(l)?t:t.set(l,new Set)).get(l);return a.add(n),()=>{null==a||a.delete(n),a.size||t.delete(l)}},r},g=Symbol(),v=(e=new WeakMap,t=new WeakMap,r=new WeakMap,i=new Set,h=new Set,v=new Set,m={},w=(e,...t)=>e.read(...t),y=(e,...t)=>e.write(...t),p=(e,t)=>{var r;return null==(r=e.unstable_onInit)?void 0:r.call(e,t)},E=(e,t)=>{var r;return null==(r=e.onMount)?void 0:r.call(e,t)},...b)=>{let S=b[0]||(t=>{if(!t)throw Error("Atom is undefined or null");let r=e.get(t);return r||(r={d:new Map,p:new Set,n:0},e.set(t,r),null==p||p(t,A)),r}),_=b[1]||(()=>{let e=[],r=t=>{try{t()}catch(t){e.push(t)}};do{m.f&&r(m.f);let e=new Set,n=e.add.bind(e);i.forEach(e=>{var r;return null==(r=t.get(e))?void 0:r.l.forEach(n)}),i.clear(),v.forEach(n),v.clear(),h.forEach(n),h.clear(),e.forEach(r),i.size&&k()}while(i.size||v.size||h.size);if(e.length)throw AggregateError(e)}),k=b[2]||(()=>{let e=[],n=new WeakSet,l=new WeakSet,a=Array.from(i);for(;a.length;){let i=a[a.length-1],o=S(i);if(l.has(i)){a.pop();continue}if(n.has(i)){if(r.get(i)===o.n)e.push([i,o]);else if(r.has(i))throw Error("[Bug] invalidated atom exists");l.add(i),a.pop();continue}for(let e of(n.add(i),c(i,o,t)))n.has(e)||a.push(e)}for(let t=e.length-1;t>=0;--t){let[n,l]=e[t],a=!1;for(let e of l.d.keys())if(e!==n&&i.has(e)){a=!0;break}a&&(D(n),N(n)),r.delete(n)}}),D=b[3]||(e=>{var c;let h,g,v=S(e);if(l(v)&&(t.has(e)&&r.get(e)!==v.n||Array.from(v.d).every(([e,t])=>D(e).n===t)))return v;v.d.clear();let y=!0,p=()=>{t.has(e)&&(N(e),k(),_())},E=v.n;try{let r=w(e,r=>{var i;if(n(e,r)){let e=S(r);if(!l(e))if("init"in r)f(r,r.init,S);else throw Error("no atom init");return a(e)}let d=D(r);try{return a(d)}finally{v.d.set(r,d.n),o(v.v)&&s(e,v.v,d),null==(i=t.get(r))||i.t.add(e),y||p()}},{get signal(){return h||(h=new AbortController),h.signal},get setSelf(){return e.write||console.warn("setSelf function cannot be used with read-only atom"),!g&&e.write&&(g=(...t)=>{if(y&&console.warn("setSelf function cannot be called in sync"),!y)try{return O(e,...t)}finally{k(),_()}}),g}});return f(e,r,S),u(r)&&(d(r,()=>null==h?void 0:h.abort()),r.then(p,p)),v}catch(e){return delete v.v,v.e=e,++v.n,v}finally{y=!1,E!==v.n&&r.get(e)===E&&(r.set(e,v.n),i.add(e),null==(c=m.c)||c.call(m,e))}}),C=b[4]||(e=>{let n=[e];for(;n.length;){let e=n.pop(),l=S(e);for(let a of c(e,l,t)){let e=S(a);r.set(a,e.n),n.push(a)}}}),O=b[5]||((e,...t)=>{let r=!0;try{return y(e,e=>a(D(e)),(t,...l)=>{var a;let o=S(t);try{if(!n(e,t))return O(t,...l);{if(!("init"in t))throw Error("atom not writable");let e=o.n,r=l[0];f(t,r,S),N(t),e!==o.n&&(i.add(t),null==(a=m.c)||a.call(m,t),C(t));return}}finally{r||(k(),_())}},...t)}finally{r=!1}}),N=b[6]||(e=>{var r;let n=S(e),l=t.get(e);if(l&&!o(n.v)){for(let[t,a]of n.d)if(!l.d.has(t)){let n=S(t);T(t).t.add(e),l.d.add(t),a!==n.n&&(i.add(t),null==(r=m.c)||r.call(m,t),C(t))}for(let t of l.d||[])if(!n.d.has(t)){l.d.delete(t);let r=I(t);null==r||r.t.delete(e)}}}),T=b[7]||(e=>{var r;let n=S(e),l=t.get(e);if(!l){for(let t of(D(e),n.d.keys()))T(t).t.add(e);l={l:new Set,d:new Set(n.d.keys()),t:new Set},t.set(e,l),null==(r=m.m)||r.call(m,e),e.write&&h.add(()=>{let t=!0;try{let r=E(e,(...r)=>{try{return O(e,...r)}finally{t||(k(),_())}});r&&(l.u=()=>{t=!0;try{r()}finally{t=!1}})}finally{t=!1}})}return l}),I=b[8]||(e=>{var r;let n=S(e),l=t.get(e);if(l&&!l.l.size&&!Array.from(l.t).some(r=>{var n;return null==(n=t.get(r))?void 0:n.d.has(e)})){for(let a of(l.u&&v.add(l.u),l=void 0,t.delete(e),null==(r=m.u)||r.call(m,e),n.d.keys())){let t=I(a);null==t||t.t.delete(e)}return}return l}),A={get:e=>a(D(e)),set:(e,...t)=>{try{return O(e,...t)}finally{k(),_()}},sub:(e,t)=>{let r=T(e).l;return r.add(t),_(),()=>{r.delete(t),I(e),_()}}};return Object.defineProperty(A,g,{value:[e,t,r,i,h,v,m,w,y,p,E,S,_,k,D,C,O,N,T,I]}),A},m=e=>(e.c||(e.c=h()),e.m||(e.m=h()),e.u||(e.u=h()),e.f||(e.f=(()=>{let e=new Set,t=()=>{e.forEach(e=>e())};return t.add=t=>(e.add(t),()=>{e.delete(t)}),t})()),e),w=d}}]);